import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTagCountryAndMarketingOptInToUsersTable1753703384445 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE users
                ADD COLUMN partner_country character varying(255),
                ADD COLUMN marketing_opt_in boolean;
            `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('users', 'marketing_opt_in');
    await queryRunner.dropColumn('users', 'tag_country');
  }
}
