import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bull';
import { ApiKeySession, ProfileEnum, ProfilesApi } from 'klaviyo-api';
import { KLAVIYO_TRACK_PROCESS_NAME, KLAVIYO_TRACK_QUEUE_NAME } from 'src/workers/klaviyo/klaviyo.constant';

export enum KlaviyoTrackEvents {}

@Injectable()
export class KlaviyoService {
  private readonly logger = new Logger(KlaviyoService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectQueue(KLAVIYO_TRACK_QUEUE_NAME) private klaviyoTrackQueue: Queue
  ) {}
  async track(email: string, event: KlaviyoTrackEvents, properties: any = {}, customerProperties: any = {}) {
    try {
      return this.klaviyoTrackQueue.add(KLAVIYO_TRACK_PROCESS_NAME, {
        event,
        email,
        properties,
        customerProperties: {
          $email: email,
          ...customerProperties,
        },
      });
    } catch (e) {
      this.logger.error(e);
    }
  }

  async trackWithMyTM(url: string, body: any) {
    try {
      return this.klaviyoTrackQueue.add(KLAVIYO_TRACK_PROCESS_NAME, {
        url,
        body,
        withMyTM: true,
      });
    } catch (e) {
      this.logger.error(e);
    }
  }

  async updateMarketingOptIn(email, optIn) {
    try {
      const privateKey = this.config.get('klaviyo.privateToken');
      const session = new ApiKeySession(privateKey);
      const profilesApi = new ProfilesApi(session);
      const listId = process.env.NODE_ENV === 'production' ? 'WKL7Cv' : 'XAZyNM';
      if (optIn) {
        const profileData = {
          data: {
            type: 'profile-subscription-bulk-create-job',
            attributes: {
              custom_source: 'Marketing Event',
              profiles: {
                data: [
                  {
                    type: 'profile',
                    attributes: {
                      email: email,
                      subscriptions: {
                        email: {
                          marketing: {
                            consent: 'SUBSCRIBED',
                          },
                        },
                      },
                    },
                  },
                ],
              },
            },
            relationships: {
              list: {
                data: {
                  type: 'list',
                  id: listId,
                },
              },
            },
          },
        };

        // Use the bulk subscribe profiles endpoint
        const response = await profilesApi.bulkSubscribeProfiles(profileData);
        console.log(`subscribed.`, response);
      } else {
        const profileData = {
          data: {
            type: 'profile-subscription-bulk-delete-job',
            attributes: {
              profiles: {
                data: [
                  {
                    type: 'profile',
                    attributes: {
                      email: email,
                      subscriptions: {
                        email: {
                          marketing: {
                            consent: 'UNSUBSCRIBED',
                          },
                        },
                      },
                    },
                  },
                ],
              },
            },
            relationships: {
              list: {
                data: {
                  type: 'list',
                  id: listId,
                },
              },
            },
          },
        };
        const response = await profilesApi.bulkUnsubscribeProfiles(profileData);
        console.log('Unsubscribe job created:', response.data);
      }
    } catch (error) {
      console.error('Error updating subscription:', error);
    }
  }
}
