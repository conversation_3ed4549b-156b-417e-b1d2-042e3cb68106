import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokeRollup } from 'src/utils/concers/stroke_rollup';
import { ThreePartyCourseService } from '../../igolf/threePartyCourse.service';
// import { sleeps } from 'src/utils/utils';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobIGolfRoundCompleteData } from './job.types';

// const RETRY_MAX = 150;
@Processor(PROCESSORS.IGolfRoundCompleteJob)
export class IGolfRoundCompleteJobProcessor {
  private readonly logger = new Logger(IGolfRoundCompleteJobProcessor.name);
  constructor(
    @InjectRepository(Round) private readonly roundRepo: Repository<Round>,
    @InjectRepository(Club) private readonly clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat) private readonly strokeStatRepo: Repository<StrokeStat>,
    @InjectRepository(HolePlayed) private readonly holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(StrokePlayed) private readonly strokePlayerRepo: Repository<StrokePlayed>,
    private readonly configService: ConfigService,
    private readonly roundService: RoundService,
    private readonly threePartyCourseService: ThreePartyCourseService,
    private readonly strokePlayedService: StrokesPlayedService
  ) {}
  @Process({ name: PROCESS_QUEUE_NAMES.IGOLF_ROUND_COMPLETE, concurrency: APP_CONCURRENCY })
  async performJob(job: JobIGolfRoundCompleteData) {
    this.logger.log(`${JSON.stringify(job.data)}`);

    const { roundId, holesPlayedModify } = job.data;
    this.logger.log('START JOB ROUND COMPLETE....');
    this.logger.log(job.data);

    const round = await this.roundRepo.findOneBy({ id: roundId });
    if (!round) {
      return;
    }
    const isTagRound = round.map_id && round.map_id.toLowerCase() === 'tag';

    let holePlayedIds = null;
    if (!holesPlayedModify) {
      const holeQueryBuilder = this.holePlayedRepo.createQueryBuilder();
      holeQueryBuilder.where({ round_id: roundId });
      holeQueryBuilder.select(['id']);
      holePlayedIds = await holeQueryBuilder.getRawMany();
      if (holePlayedIds.length > 0) {
        holePlayedIds = holePlayedIds.map((hole: any) => hole.id);
      }
    } else {
      holePlayedIds = holesPlayedModify;
    }

    let strokes = [];
    await this.updateIGolfStats(roundId);
    strokes = await this.getStrokes(strokes, holePlayedIds);

    strokes = strokes.map((s) => {
      if (!s.lie) {
        s.lie = 'Hole Boundary';
      }
      return s;
    });

    // # generate stroke stats
    const strokeRollup = new StrokeRollup(
      round,
      this.roundRepo,
      this.strokeStatRepo,
      this.clubRepo,
      this.holePlayedRepo,
      this.strokePlayedService,
      this.threePartyCourseService,
      this.roundService
    );
    await strokeRollup.iGolfGenerateStats(round, strokes, isTagRound);
  }
  private async updateIGolfStats(roundId: number) {
    await this.roundService.iGolfStatSql(roundId);
    await this.roundService.statSql(roundId);
  }

  private async getStrokes(strokes: any[], holePlayedIds: any) {
    strokes = await this.strokePlayerRepo.find({
      where: { hole_played_id: In(holePlayedIds) },
      select: [
        'id',
        'hole_played_id',
        'difficult',
        'lie',
        'coords',
        'round_id',
        'club_id',
        'azimuth_to_pin',
        'result_from_pin',
        'shot_azimuth',
        'result_angle_from_pin',
        'ordinal',
      ],
    });
    return strokes;
  }
  private async countStrokes(holePlayedIds: any) {
    const count = await this.strokePlayerRepo.count({
      where: { hole_played_id: In(holePlayedIds) },
    });
    return count;
  }
}
