import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { RoundAuditUtilsService } from 'src/round-audit-util/round-audit-util.service';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokeRollup } from 'src/utils/concers/stroke_rollup';
import { sleeps } from 'src/utils/utils';
import { ThreePartyCourseService } from '../../igolf/threePartyCourse.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobIGolfRoundCompleteData } from './job.types';

const RETRY_MAX = 30;
@Processor(PROCESSORS.IGolfRoundDrivingDispersionJob)
export class IGolfRoundDrivingDispersionJobProcessor {
  private readonly logger = new Logger(IGolfRoundDrivingDispersionJobProcessor.name);
  constructor(
    @InjectRepository(Round) private readonly roundRepo: Repository<Round>,
    @InjectRepository(Club) private readonly clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat) private readonly strokeStatRepo: Repository<StrokeStat>,
    @InjectRepository(HolePlayed) private readonly holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(StrokePlayed) private readonly strokePlayerRepo: Repository<StrokePlayed>,
    private readonly configService: ConfigService,
    private readonly roundService: RoundService,
    private readonly threePartyCourseService: ThreePartyCourseService,
    private roundAuditUtilService: RoundAuditUtilsService,
    private readonly strokePlayedService: StrokesPlayedService
  ) {}
  @Process({ name: PROCESS_QUEUE_NAMES.IGOLF_ROUND_DRIVING_DISPERSION, concurrency: APP_CONCURRENCY })
  async performJob(job: JobIGolfRoundCompleteData) {
    this.logger.log('START JOB ROUND DRIVING DISPERSION....');
    this.logger.log(`DATA: ${JSON.stringify(job.data)}`);
    const { roundId, holesPlayedModify } = job.data;
    const round = await this.roundRepo.findOneBy({ id: roundId });
    if (!round) {
      return;
    }
    let holePlayedIds = null;
    if (!holesPlayedModify || holesPlayedModify.length == 0) {
      const holeQueryBuilder = this.holePlayedRepo.createQueryBuilder();
      holeQueryBuilder.where({ round_id: roundId });
      holeQueryBuilder.select(['id']);
      holePlayedIds = await holeQueryBuilder.getRawMany();
      if (holePlayedIds.length > 0) {
        holePlayedIds = holePlayedIds.map((hole: any) => hole.id);
      }
    } else {
      holePlayedIds = holesPlayedModify;
    }
    let isCreateStrokeStatsDone = false;
    let strokes = [];
    let strokesStats = [];
    let retryMax = RETRY_MAX;
    // # generate stroke stats
    const strokeRollup = new StrokeRollup(
      round,
      this.roundRepo,
      this.strokeStatRepo,
      this.clubRepo,
      this.holePlayedRepo,
      this.strokePlayedService,
      this.threePartyCourseService
    );
    const countStrokesPlayed = await this.countStrokesPlayed(holePlayedIds);
    while (!isCreateStrokeStatsDone) {
      this.logger.debug(`GET STROKES.....`);
      const countStrokes = await this.countStrokesStats(holePlayedIds);
      retryMax--;
      this.logger.debug(`STROKES SIZE: ${countStrokes}`);
      if (countStrokes < countStrokesPlayed && retryMax > 0) {
        this.logger.debug(`WAIT STROKE CREATED DONE!....`);
        await sleeps(3);
      } else {
        if (countStrokes >= countStrokesPlayed) {
          retryMax = RETRY_MAX;
          isCreateStrokeStatsDone = true;
          strokes = await this.getStrokes(strokes, holePlayedIds);
          strokesStats = await this.getStrokesStats(holePlayedIds);
        } else {
          this.logger.debug(`WAIT STROKE CREATED DONE!....`);
          await sleeps(3);
        }
      }
      if (retryMax == 0) {
        this.logger.debug(`REACHED MAX TIME WAIT!....`);
        isCreateStrokeStatsDone = true;
        // Regenerate StrokeStats
        this.logger.debug(`REGENERATE STROKE STATS!....`);
        await this.strokeStatRepo.delete({ hole_played_id: In(holePlayedIds) });
        return this.roundAuditUtilService.queueCompleteRound(round, holesPlayedModify, countStrokesPlayed);
      }
    }

    await strokeRollup.updateDrivingDispersion(round, strokes, strokesStats);
  }

  private async getStrokes(strokes: any[], holePlayedIds: any) {
    strokes = await this.strokePlayerRepo.find({
      where: { hole_played_id: In(holePlayedIds) },
      select: [
        'id',
        'hole_played_id',
        'difficult',
        'lie',
        'coords',
        'round_id',
        'club_id',
        'azimuth_to_pin',
        'result_from_pin',
        'shot_azimuth',
        'result_angle_from_pin',
        'ordinal',
      ],
    });
    return strokes;
  }
  private async getStrokesStats(holePlayedIds: any) {
    return await this.strokeStatRepo.find({
      where: { hole_played_id: In(holePlayedIds) },
      select: ['id', 'hole_played_id', 'stroke_played_id', 'stroke_ordinal', 'left', 'right'],
    });
  }
  private async countStrokesStats(holePlayedIds: any) {
    const count = await this.strokeStatRepo.count({
      where: { hole_played_id: In(holePlayedIds) },
    });
    return count;
  }
  private async countStrokesPlayed(holePlayedIds: any) {
    const count = await this.strokePlayerRepo.count({
      where: { hole_played_id: In(holePlayedIds) },
    });
    return count;
  }
}
