export enum PROCESSORS {
  IGolfRoundCompleteJob = 'i-golf-round-complete',
  ForceRoundCompleteJob = 'force-round-complete',
  CalcHandicapJob = 'calc-handicap',
  CalculateAverageScoreJob = 'calculate-average-score',
  CalculateAverageScoreClassicJob = 'calculate-average-score-classic',
  CourseUpdateJob = 'course-update',
  IGolfRoundDrivingDispersionJob = 'i-golf-round-driving-dispersion',
  MassParentalAcceptanceJob = 'mass-parental-acceptance',
  MyTmEventsJob = 'my-tm-events',
  ParentalAcceptanceJob = 'parental-acceptance',
  ResetAgreementsJob = 'reset-agreements',
  RoundAuditImportJob = 'round-audit-import',
  RoundAuditProcessJob = 'round-audit-process',
  RoundAuditUpdateJob = 'round-audit-update',
  RoundCompleteJob = 'round-complete',
  RoundGenerateJob = 'round-generate',
  RoundStatJob = 'round-stat',
  RoundUpdateJob = 'round-update',
  SendWelcomeEmailJob = 'send-welcome-email',
  SimpleScoreToParJob = 'simple-score-to-par',
  StatRollupJob = 'stat-rollup',
  StrokeMissingCoordsJob = 'stroke-missing-coords',
  StrokeRollupJob = 'stroke-rollup',
  StrokeStatsUpdateTeeJob = 'stroke-stats-update-tee',
  SyncAddCdmClubJob = 'sync-add-cdm-club',
  SyncAddWitbJob = 'sync-add-witb',
  SyncConsumerClubsJob = 'sync-consumer-clubs',
  SyncConsumerClubsUpdateJob = 'sync-consumer-clubs-update',
  SyncConsumerJob = 'sync-consumer',
  SyncGhinHandicapIndexJob = 'sync-ghin-handicap-index',
  SyncUpdateClubsFromCdmJob = 'sync-update-clubs-from-cdm',
  SyncUpdateListWitbJob = 'sync-update-list-witb',
  SyncUpdateMrpIdJob = 'sync-update-mrp-id',
  SyncUpdateGhinNumberJob = 'sync-update-ghin-number',
  SyncClubsToCDM = 'sync-clubs-to-cdm',
  SyncUpdateUserFromCdmJob = 'sync-update-user-from-cdm',
  SyncUpdateWitbJob = 'sync-update-witb',
  UnderAgeEmailJob = 'under-age-email',
  ClearShotNotInHole = 'clear-shot-not-in-hole',
  PostScoreToGHIN = 'post-score-to-GHIN',
  LogRequestIGolfSI = 'log-request-igolf-SI',
  RemoveUserLogCourse = 'remove-user-log-course-daily',
}
export enum PROCESS_QUEUE_NAMES {
  IGOLF_ROUND_COMPLETE = 'IGOLF_ROUND_COMPLETE',
  FORCE_ROUND_COMPLETE = 'FORCE_ROUND_COMPLETE',
  IGOLF_ROUND_DRIVING_DISPERSION = 'IGOLF_ROUND_DRIVING_DISPERSION',
  CALC_HANDICAP = 'CALC_HANDICAP',
  CALCULATE_AVERAGE_SCORE = 'CALCULATE_AVERAGE_SCORE',
  CALCULATE_AVERAGE_CLASSIC_SCORE = 'CALCULATE_AVERAGE_CLASSIC_SCORE',
  SEND_EMAIL = 'SEND_EMAIL',
  COURSE_UPDATE = 'COURSE_UPDATE',
  ROUND_STATS = 'ROUND_STATS',
  MY_TM_EVENTS = 'MY_TM_EVENTS',
  ROUND_AUDIT_IMPORT = 'ROUND_AUDIT_IMPORT',
  ROUND_AUDIT_PROCESS = 'ROUND_AUDIT_PROCESS',
  STAT_ROLLUP_JOB = 'STAT_ROLLUP_JOB',
  ROUND_GENERATOR = 'ROUND_GENERATOR',
  ROUND_UPDATE = 'ROUND_UPDATE',
  SIMPLE_SCORE_TO_PAR = 'SIMPLE_SCORE_TO_PAR',
  STROKE_MISSING_COORDS_JOB = 'STROKE_MISSING_COORDS_JOB',
  STROKE_STATS_UPDATE_TEE = 'STROKE_STATS_UPDATE_TEE',
  SYNC_ADD_CDM_CLUB = 'SYNC_ADD_CDM_CLUB',
  SYNC_ADD_WITB = 'SYNC_ADD_WITB',
  SYNC_CONSUMER_CLUB = 'SYNC_CONSUMER_CLUB',
  SYNC_CONSUMER_CLUB_UPDATE = 'SYNC_CONSUMER_CLUB_UPDATE',
  SYNC_CONSUMER = 'SYNC_CONSUMER',
  SYNC_UPDATE_CLUBS_FROM_CDM = 'SYNC_UPDATE_CLUBS_FROM_CDM',
  SYNC_UPDATE_LIST_WITB = 'SYNC_UPDATE_LIST_WITB',
  SYNC_UPDATE_MRP_ID = 'SYNC_UPDATE_MRP_ID',
  SYNC_UPDATE_GHIN_NUMBER = 'SYNC_UPDATE_GHIN_NUMBER',
  SYNC_UPDATE_USER_FROM_CDM = 'SYNC_UPDATE_USER_FROM_CDM',
  SYNC_UPDATE_WITB = 'SYNC_UPDATE_WITB',
  SYNC_CLUBS_TO_CDM = 'SYNC_CLUBS_TO_CDM',
  SYNC_GHIN_HANDICAP_INDEX = 'SYNC_GHIN_HANDICAP_INDEX',
  CREATE_ROUND_MULTIPLE_PLAYER = 'CREATE_ROUND_MULTIPLE_PLAYER',
  CREATE_ROUND_MULTIPLE_PLAYER_SCORE = 'CREATE_ROUND_MULTIPLE_PLAYER_SCORE',
  CREATE_ROUND_CREATE_STROKE = 'CREATE_ROUND_CREATE_STROKE',
  CLEAR_SHOT_NOT_IN_HOLE = 'CLEAR_SHOT_NOT_IN_HOLE',
  POST_SCORE_TO_GHIN = 'POST_SCORE_TO_GHIN',
  LOG_REQUEST_IGOLF_SI = 'LOG_REQUEST_IGOLF_SI',
  REMOVE_USER_LOG_COURSE = 'REMOVE_USER_LOG_COURSE',
}
export const APP_CONCURRENCY = 4;
