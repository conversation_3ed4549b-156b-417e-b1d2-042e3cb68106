import { getDistancePoints } from 'src/utils/utils';
import { Stats } from '../stats';

export class SmartGolfStatsSandSaves {
  // attr_reader :strokes
  strokes: any;
  allStrokes: any;
  constructor(strokes: any, allStrokes: any, hole_played: any) {
    // console.dir({ hole_played }, { depth: null });
    const pin_location = hole_played?.pin_location?.coordinates;

    this.strokes = this.find_sand_save_opportunities(strokes.flat(), pin_location);
    this.allStrokes = allStrokes;
  }
  // Initialize
  //
  // These are sand save opportunities
  //
  // Get all sand saved opportunities where the ball starts in a bunker
  // 30 yards or less away from the flag.
  //
  //
  // initialize(strokes) {
  //   // @strokes = find_sand_save_opportunities( [ strokes ].flatten )
  // }

  //
  // Total number of sand saved opportunities
  //
  // Where the ball starts in a bunker, 30 yards or less away from the flag.
  //
  numberOfSandSavedOpportunities() {
    // strokes.size
    return this.strokes?.length || 0;
  }

  //
  // Total number of sands saved
  //
  // This is the number of actual sand saves the golfer has had.
  //
  numberOfSandsSaved() {
    // sands_saved.size
    const sandsSave: any = this.sands_saved();
    return sandsSave?.length || 0;
  }

  //
  // Calculate Sand Saves
  //
  // ## About
  //
  // Need to count number of sand save opportunities. If player hits a bunker
  // shot within 30 yds of green this is a sand save opportunity. A sand save
  // occurs if the player has the ball in the hole after the next shot. For
  // my calculations I said, if shot 3 is in the bunker and shot 4 ends distance
  // from hole, this is a sand save.
  //
  //
  calculate() {
    // return 0 if number_of_sand_saved_opportunities.zero? || sands_saved.empty?
    // number_of_sands_saved.to_f / number_of_sand_saved_opportunities.to_f * 100
    const nbSandSavedOpp = this.numberOfSandSavedOpportunities();
    const nbSandSave = this.numberOfSandsSaved();
    if (nbSandSavedOpp == 0 || nbSandSave == 0) {
      return 0;
    }

    return (nbSandSave / nbSandSavedOpp) * 100;
  }

  //
  // Where the ball starts in a bunker, 30 yards or less away from the flag.
  //
  find_sand_save_opportunities(shots, pin_location) {
    //   shots.select do |s|
    //   s.lie =~ /bunker/i &&
    //     s.coords.distance(s.pin_location).round <= THIRTY_YARDS
    // end

    return shots.filter((s) => {
      return (
        s?.lie?.toLowerCase().trim() == 'bunker' &&
        getDistancePoints(s?.coords?.coordinates, pin_location) <= Stats.THIRTY_YARDS
      );
    });
  }

  //
  // Sands Saved
  //
  // ==== About
  //
  // It is only a sand saved if the first 1 or 2 strokes complete the
  // hole being played.
  //
  // ==== Returns
  //
  // Returns an array
  //
  sands_saved() {
    //   strokes.select do |s|
    //   ( ( s.all_strokes.length - 1 ) - ( s.all_strokes.index(s) ) ) < 2
    // end
    return this.strokes.filter((s: any) => {
      const indexStroke = this.allStrokes.findIndex((stroke: any) => stroke.id == s.id);
      return this.allStrokes.length - 1 - indexStroke < 2;
    });
  }
}
