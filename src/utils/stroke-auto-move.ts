import * as turf from '@turf/turf';
import { Point } from 'geojson';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';
import { Coordinates } from './concers/coordinates';
import { includeStr } from './utils';

export class StrokeAutoMove {
  private stroke: any;
  private all_strokes: any;
  private hole_played: any;
  private holePlayedRepo: any;
  private threePartyCourseService: ThreePartyCourseService;
  private tee_center_coords: any;
  constructor(stroke, all_strokes, hole_played, holePlayedRepo, threePartyCourseService) {
    this.stroke = stroke;
    this.all_strokes = all_strokes;
    this.hole_played = hole_played;
    this.holePlayedRepo = holePlayedRepo;
    this.threePartyCourseService = threePartyCourseService;
    // @stroke           = stroke
    // @all_strokes      = @stroke.all_strokes
    // @hole_played      = @stroke.hole_played
    // @hole             = @hole_played.hole
    // @round            = @stroke.hole_played.round
    // if @round.map_id == Round::MAP_TYPE_IGOLF
    //   @course ||= Course::Igolf.new(@round.igolf_course_id, nil, @round.user_id)
    // else
    //   @course ||= @round.course
    // end
    if (!hole_played.tee_center) {
      let tee_center_point = null;
      this.threePartyCourseService
        .coordinatesFor(hole_played.name, 'tee', hole_played.igolf_course_id)
        .then((result) => {
          if (!result) {
            return;
          }
          tee_center_point = result[result.length - 1][0];
          const tee_center: Point = {
            type: 'Point',
            coordinates: tee_center_point,
          };
          this.tee_center_coords = tee_center_point;
          this.holePlayedRepo.update({ id: hole_played.id }, { tee_center });
        });
      // @course.send( :tee_center, stroke.hole_played.name.to_i);

      // @hole_played.update(tee_center: factory.point(tee_center_point[1], tee_center_point[0]))
    } else {
      this.tee_center_coords = hole_played.tee_center.coordinates;
    }
  }
  async getTeeCenter() {
    if (this.hole_played?.tee_center?.coordinates) {
      return this.hole_played.tee_center.coordinates;
    }
    let tee_center_point = null;
    const result = await this.threePartyCourseService.coordinatesFor(
      this.hole_played.name,
      'tee',
      this.hole_played.igolf_course_id
    );
    if (!result) {
      return;
    }
    tee_center_point = result[result.length - 1][0];
    const tee_center: Point = {
      type: 'Point',
      coordinates: tee_center_point,
    };
    this.tee_center_coords = tee_center_point;
    await this.holePlayedRepo.update({ id: this.hole_played.id }, { tee_center });
    return tee_center_point;
  }

  get_coords() {
    // return hole_played.tee_center if stroke.first_stroke? && not_on_hole?(stroke.coords)
    // if stroke.lie =~ /green/i
    //   get_random_point_on_the_green
    // else
    //   factory.point( midpoint.lon, midpoint.lat )
    // end
    const isFirstStroke = this.isFirstStroke(this.stroke);
    const isNotOnHole = this.isNotOnHole(this.stroke?.coords?.coordinates);
    if (isFirstStroke && isNotOnHole) {
      return this.getTeeCenter();
    }

    if (includeStr(this.stroke.lie, 'green')) {
      return this.get_random_point_on_the_green();
    } else {
      const midPoint = this.midpoint();
      return midPoint;
    }
  }

  //   #
  //   # Get a random point on the green
  //   #
  get_random_point_on_the_green() {
    return this.threePartyCourseService.get_random_point_within_shape(
      'green',
      this.hole_played.name,
      this.hole_played.igolf_course_id
    );
  }

  midpoint() {
    const prevStrokeCoord = this.previousStrokeCoordinates(this.stroke);
    const nextStrokeCoord = this.nextStrokeCoordinates(this.stroke);
    const prevPoint = turf.point(prevStrokeCoord);
    const nextPoint = turf.point(nextStrokeCoord);
    const midPoint = turf.midpoint(prevPoint, nextPoint);
    return midPoint.geometry.coordinates;
  }
  isLastStroke(stroke) {
    return this.all_strokes[this.all_strokes.length - 1].id == stroke.id;
  }
  previousStroke(stroke) {
    const indx = this.all_strokes.findIndex((s: any) => +s.id == +stroke.id);
    return indx <= 0 ? null : this.all_strokes[indx - 1];
  }
  isFirstStroke(stroke) {
    if (this.all_strokes.length == 0) {
      return false;
    }
    return this.all_strokes[0].id == stroke.id;
  }
  generateNextStroke(stroke: any) {
    const indx = this.all_strokes.findIndex((s: any) => +s.id == +stroke.id);
    const next = indx >= this.all_strokes.length - 1 ? null : this.all_strokes[indx + 1];
    return next;
  }

  previousStrokeCoordinates(shot) {
    if (this.isFirstStroke(shot) && this.isNotOnHole(shot.coords.coordinates)) {
      return this.hole_played?.tee_center?.coordinates;
    }
    if (this.isOnHole(shot.coords.coordinates)) {
      return shot?.coords?.coordinates;
    }

    return this.previousStrokeCoordinates(this.previousStroke(shot));
  }
  pinLocation() {
    const coords = this.hole_played.pin_location.coordinates;
    return coords;
  }
  nextStrokeCoordinates(shot) {
    if (!shot) {
      return this.pinLocation();
    }
    if (this.isLastStroke(shot) && this.isNotOnHole(shot?.coords?.coordinates)) {
      return this.pinLocation();
    }
    if (this.isOnHole(shot?.coords?.coordinates)) {
      return shot?.coords?.coordinates;
    }

    return this.nextStrokeCoordinates(this.generateNextStroke(shot));
  }

  isOnHole(coords) {
    // return false if coordinates_missing?(coords) || !hole.present?
    // ::Polygon.within(hole.id, coords).exists?
    const isCoordMissing = Coordinates.coordinatesMissing(coords);
    if (isCoordMissing || !this.hole_played) {
      return false;
    }
    return true;
  }

  isNotOnHole(coords) {
    return !this.isOnHole(coords);
    // !on_hole?(coords)
  }
}
