import { ApiProperty } from '@nestjs/swagger';
import * as bcrypt from 'bcryptjs';
import { isEmpty } from 'class-validator';
import secureRandom from 'secure-random';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityHelper } from 'src/utils/entity-helper';

export enum SG_BASELINES {
  pga = 'Pro',
  scratch = 'Scratch',
  five = 5,
  ten = 10,
  fifteen = 15,
  twenty = 20,
}

@Entity({ name: 'users' })
export class User extends EntityHelper {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ unique: true, nullable: true })
  @ApiProperty({ example: '<EMAIL>' })
  email: string | null;

  @Column({ nullable: true })
  encrypted_password: string;

  @Column({ nullable: true })
  reset_password_token: string;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ example: new Date().toISOString() })
  reset_password_sent_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ example: new Date().toISOString() })
  remember_created_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  sign_in_count: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ example: new Date().toISOString() })
  current_sign_in_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ example: new Date().toISOString() })
  last_sign_in_at: Date;

  @Column({ nullable: true })
  @ApiProperty({ example: '**********' })
  current_sign_in_ip: string;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 1 })
  failed_attempts: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'token' })
  unlock_token: string;

  @Column({ nullable: false })
  @ApiProperty({ example: 'US' })
  country: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '+1.0' })
  handicap: string;

  @Column({ nullable: true, type: 'date' })
  @ApiProperty({ example: '0001-09-27' })
  birthday: Date | string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'left' })
  handed: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '1' })
  ghin_id: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'ghin_email@example' })
  ghin_email: string;

  @Column({ nullable: true })
  @ApiProperty({ example: false })
  ghin_gpa_status: boolean;

  @Column({ nullable: true })
  @ApiProperty({ example: '1' })
  canada_card_id: string;

  @Column({ nullable: true })
  @ApiProperty({ example: false })
  is_spam_igolf: boolean;

  @Column({ nullable: true })
  @ApiProperty({ example: false })
  is_user_igolf_blocked: boolean;

  @BeforeInsert()
  async beforeInsertActions() {
    const salt = await bcrypt.genSalt(10);
    this.encrypted_password = await bcrypt.hash(`${this.encrypted_password}${process.env.BCRYPT_PEPPER}`, salt);
    this.share_url = secureRandom.randomBuffer(16).toString('base64');
    this.token = secureRandom.randomBuffer(16).toString('hex');
    this.handed = 'right';
    this.status = 0;
    this.role = 0;
    this.detectBaseline();
  }

  @Column({ nullable: true, default: 'scratch' })
  @ApiProperty({ example: 'twenty' })
  strokes_gained_baseline: string;

  detectBaseline() {
    if (this.strokes_gained_baseline == '' || isEmpty(this.strokes_gained_baseline)) {
      this.strokes_gained_baseline = 'scratch';
      return;
    }
    const sgBaseLineVal = Object.values(SG_BASELINES).map((k) => k.toString().toLowerCase());
    const sgBaseLineKeys = Object.keys(SG_BASELINES);
    const userBaseLineVal = sgBaseLineVal.find(
      (val) => val.toLowerCase() == this.strokes_gained_baseline.toLowerCase()
    );
    if (userBaseLineVal) {
      const indexVal = sgBaseLineVal.indexOf(userBaseLineVal);
      this.strokes_gained_baseline = sgBaseLineKeys[indexVal];
      return;
    }
    const userBaseLineKey = sgBaseLineKeys.find(
      (key) => key.toLowerCase() == this.strokes_gained_baseline.toLowerCase()
    );
    if (userBaseLineKey) {
      this.strokes_gained_baseline = userBaseLineKey;
      return;
    }
    return 'scratch';
  }

  @Column({ nullable: true })
  @ApiProperty({ example: 'Woodard' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'male' })
  gender: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'v1440077360/bl7kzviobl3b4jidn5w6.jpg' })
  avatar: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '<EMAIL>' })
  guardian_email: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '-1RKpeglSgfqe-Pul8Yf_A' })
  share_url: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '3145d22b-0b7f-4419-b498-546aeb0031c3' })
  parental_uuid: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '16-aprilia-rsv4RF-8.jpg' })
  avatar_file_name: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'image/jpeg' })
  avatar_content_type: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '0022471e243f082b9fe1a6a4ff019345' })
  token: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'Android Device' })
  signup_by: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'Carrickfergus Golf Club' })
  home_course_name: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '1' })
  iGolf_home_course_id: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '60515' })
  postal_code: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'Nathaniel' })
  first_name: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '3145d22b-0b7f-4419-b498-546aeb0031c3' })
  cdm_id: string;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 9883 })
  avatar_file_size: number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 1 })
  status: number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 1 })
  role: number;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: true })
  third_party_email_opt_in: boolean;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: true })
  tmag_email_opt_in: boolean;

  @Column({ nullable: true, type: 'boolean', default: false })
  @ApiProperty({ example: true })
  delete_data: boolean;

  @Column({ type: 'timestamp', default: null })
  @ApiProperty({ example: new Date().toISOString() })
  deactivated: Date;

  @Column({ type: 'timestamp', default: null })
  @ApiProperty({ example: new Date().toISOString() })
  start_mytm: Date;

  @Column({ type: 'timestamp', default: null })
  @ApiProperty({ example: new Date().toISOString() })
  guardian_agreed_on: Date;

  @Column({ type: 'timestamp', default: null })
  @ApiProperty({ example: new Date().toISOString() })
  avatar_updated_at: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  accepted_terms_on: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  accepted_privacy_on: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @BeforeInsert()
  setCreatedAt() {
    this.created_at = new Date();
    this.updated_at = this.created_at;
  }
  @BeforeUpdate()
  setUpdatedAt() {
    this.updated_at = new Date();
  }

  @Column({ nullable: true })
  @ApiProperty({ example: 'partner_name' })
  partner_name: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'partner_user_id' })
  partner_user_id: string;

  @Column({ nullable: true })
  @ApiProperty({ example: '<EMAIL>' })
  partner_user_email: string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'USA' })
  partner_country: string;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: true })
  marketing_opt_in: boolean;
}
