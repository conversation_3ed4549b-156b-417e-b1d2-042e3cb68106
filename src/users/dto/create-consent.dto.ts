import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateConsentDto {
  @ApiProperty({ example: '<EMAIL>' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @ApiProperty({ example: 'USA' })
  @Transform(({ value }) => value?.toUpperCase().trim())
  @IsString()
  country: string;

  @ApiProperty({ example: true })
  @IsNotEmpty()
  @IsBoolean()
  marketing_opt_in: boolean;
}
