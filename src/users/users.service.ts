import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { plainToClass } from 'class-transformer';
import moment from 'moment';
import { Repository } from 'typeorm';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { CreatePartnerUserDto, CreateUserDto } from 'src/users/dto/create-user.dto';
import { CreateConsentDto } from 'src/users/dto/create-consent.dto';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';
import { User } from 'src/users/entities/user.entity';
import { EntityCondition } from 'src/utils/types/entity-condition.type';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { AuthService } from '../auth/auth.service';
import { CdmService } from '../cdm/cdm.service';
import { ClientsService } from '../clients/clients.service';
import { StrokesStatsService } from '../strokes-stats/strokes-stats.service';
import { KlaviyoService } from '../klaviyo/klaviyo.service';
import { parseStrDate } from '../utils/utils';
import { DeviceToken } from './entities/device-token.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(DeviceToken)
    private deviceTokensRepository: Repository<DeviceToken>,
    @Inject(forwardRef(() => RoundService)) private roundsService: RoundService,
    @Inject(forwardRef(() => StrokesStatsService)) private strokesStatsService: StrokesStatsService,
    @InjectQueue(PROCESSORS.SyncUpdateClubsFromCdmJob) private syncUpdateClubsFromCdmJob: Queue,
    @Inject(forwardRef(() => AuthService)) private authService: AuthService,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @Inject(forwardRef(() => ClientsService)) private clientsService: ClientsService,
    @Inject(forwardRef(() => KlaviyoService)) private klaviyoService: KlaviyoService
  ) {}

  create(userDto: CreateUserDto) {
    return this.usersRepository.save(this.usersRepository.create(userDto.user));
  }

  createWithEntity(user: User) {
    return this.usersRepository.save(user);
  }

  saveDeviceToken(userId: number, token: string) {
    return this.deviceTokensRepository.save(
      this.deviceTokensRepository.create({ user_id: userId, device_token: token })
    );
  }

  getDeviceTokens(userId: number) {
    return this.deviceTokensRepository.find({ where: { user_id: userId } });
  }

  deleteDeviceToken(userId: number, token: string) {
    return this.deviceTokensRepository.delete({ user_id: userId, device_token: token });
  }

  findManyWithPagination(paginationOptions: IPaginationOptions) {
    return this.usersRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });
  }

  findOne(fields: EntityCondition<User>) {
    return this.usersRepository.findOne({
      where: fields,
    });
  }

  count(fields: EntityCondition<User>) {
    return this.usersRepository.count({
      where: fields,
    });
  }

  async update(id: number, updateProfileDto: UpdateUserDto) {
    await this.usersRepository.update({ id }, updateProfileDto.user);
    return this.findOne({ id });
  }

  async updateSIAccount(id: number, options: any) {
    console.log(options);
    await this.usersRepository.update({ id }, options);
    return this.findOne({ id });
  }

  async updateStartMyTm(userId: number, startMyTM: Date) {
    return this.usersRepository.update({ id: userId }, { start_mytm: startMyTM });
  }
  async updateGHINId(id: number, ghinId: string) {
    await this.usersRepository.update({ id }, { ghin_id: ghinId });
    return this.findOne({ id });
  }

  async updateCanadaCardId(id: number, cardId: string) {
    await this.usersRepository.update({ id }, { canada_card_id: cardId });
    return this.findOne({ id });
  }

  updateUserCdmConsumerId(userId: number, cdmConsumerId: string) {
    return this.usersRepository.update({ id: userId }, { cdm_id: cdmConsumerId });
  }

  softDelete(id: number) {
    return this.usersRepository.update({ id }, { deactivated: new Date() });
  }

  async forceDelete(id: number) {
    try {
      const user = await this.usersRepository.findOne({
        where: { id },
      });
      if (!user) return { success: false, msg: 'User not found!' };
      await this.strokesStatsService.removeByUserId(id);
      await this.usersRepository.delete({ id });
      return { success: true };
    } catch (err) {
      console.log('ERR_FORCE_DELETE', err);
      return { success: false };
    }
  }

  transformUser(user: User): User {
    if (user.birthday) {
      user.birthday = moment(user.birthday).format('YYYY-MM-DD');
    }
    delete user.encrypted_password;
    delete user.reset_password_token;
    delete user.reset_password_sent_at;
    delete user.sign_in_count;
    delete user.current_sign_in_at;
    delete user.last_sign_in_at;
    delete user.current_sign_in_ip;
    delete user.failed_attempts;
    delete user.unlock_token;
    delete user.created_at;
    delete user.updated_at;
    delete user.avatar_file_name;
    delete user.avatar_content_type;
    delete user.parental_uuid;
    delete user.delete_data;
    delete user.deactivated;
    delete user.guardian_agreed_on;
    user.avatar = user.avatar || '/images/original/missing.png';
    user.iGolf_home_course_id = user.iGolf_home_course_id || '';
    user.home_course_name = user.home_course_name || '';
    user.first_name = user.first_name || '';
    user.name = user.name || '';
    user.handicap = user?.handicap?.replace('-', '+');
    return user;
  }

  transformCreateUser(user: User): User {
    if (user.birthday) {
      user.birthday = moment(user.birthday).format('YYYY-MM-DD');
    }
    delete user.encrypted_password;
    delete user.reset_password_token;
    delete user.reset_password_sent_at;
    delete user.current_sign_in_at;
    delete user.last_sign_in_at;
    delete user.current_sign_in_ip;
    delete user.failed_attempts;
    delete user.unlock_token;
    delete user.remember_created_at;
    return user;
  }

  async findRounds(
    id: number,
    pagingOptions: IPaginationOptions,
    light: boolean,
    completed: boolean,
    usga: boolean,
    golfnet: boolean
  ) {
    return await this.roundsService.findAll(id, pagingOptions, { completed, usga, golfnet });
  }

  async findRoundTMAndUSGA(id: number, pagingOptions: IPaginationOptions, light: boolean, completed: boolean) {
    return await this.roundsService.findRoundTMAndUSGA(id, pagingOptions, { completed });
  }

  async syncCDMClubs(user) {
    try {
      await this.syncUpdateClubsFromCdmJob.add(
        PROCESS_QUEUE_NAMES.SYNC_UPDATE_CLUBS_FROM_CDM,
        { userId: user.id },
        OPTIONS_JOB_DEFAULT
      );
    } catch (error) {
      console.log(`ADD QUEUE SYNC_UPDATE_CLUBS_FROM_CDM FAIL`);
      console.log(error);
    }
  }

  async createPartnerUser(partnerUserDto: CreatePartnerUserDto, company: string): Promise<User> {
    const clientCompany = company || 'partner';
    const { tag_user_id, tag_user_email } = partnerUserDto.user;

    if (!tag_user_email) {
      throw new Error('Email is required');
    }

    // Check if a user already exists
    const existingUser = await this.findOne({ email: tag_user_email });
    if (existingUser) {
      if (!existingUser.partner_user_email) {
        await this.usersRepository.update(
          { id: existingUser.id },
          {
            partner_user_email: tag_user_email,
            partner_user_id: tag_user_id,
            partner_name: clientCompany.toLowerCase(),
          }
        );
      }
      return existingUser;
    }

    const partnerName = clientCompany ? clientCompany.toUpperCase() : 'partner';
    const userDto: CreateUserDto = plainToClass(CreateUserDto, partnerUserDto);
    const currentDate = moment().format('DD/MM/YYYY');
    const birthday = moment().subtract(40, 'year').format('DD/MM/YYYY');
    const password = '12345@Tag';

    userDto.user.birthday = parseStrDate(birthday);
    userDto.user.accepted_privacy_on = parseStrDate(currentDate);
    userDto.user.accepted_terms_on = parseStrDate(currentDate);
    userDto.user.encrypted_password = password;
    userDto.user.password = password;
    userDto.user.password_confirmation = password;
    userDto.user.email = tag_user_email;
    userDto.user.partner_user_email = tag_user_email;
    userDto.user.partner_name = partnerName;
    userDto.user.partner_user_id = tag_user_id;
    userDto.user.accepted_both = 1;
    userDto.user.country = 'us';
    userDto.user.first_name = partnerName;
    userDto.user.third_party_email_opt_in = true;
    userDto.user.tmag_email_opt_in = true;
    userDto.user.gender = 'male';

    // Check if email already exists in Auth0
    const isEmailExists = await this.authService.isEmailAlreadyExists(userDto.user.email);
    if (isEmailExists) {
      throw new Error('Email has already been taken');
    }

    // Create Auth0 user
    const [error] = await this.authService.createAuth0User(userDto.user.email, userDto.user.password);
    if (error) {
      throw new Error(`Auth0 error: ${error.message}`);
    }

    // Create user in database
    const user = await this.create(userDto);

    // Sync with CDM
    try {
      await this.cdmService.syncConsumerByUserId(user.id);
    } catch (error) {
      console.log('CDM sync error:', error);
    }

    return user;
  }

  async createOrUpdateConsent(consentDto: CreateConsentDto, company: string): Promise<User> {
    const { email, country, marketing_opt_in } = consentDto;

    // Check if user already exists
    let user = await this.findOne({ email });

    if (user) {
      // Update existing user with new tag_country and marketing_opt_in
      await this.usersRepository.update(
        { id: user.id },
        {
          partner_country: country,
          marketing_opt_in: marketing_opt_in,
        }
      );
      // Fetch updated user
      user = await this.findOne({ id: user.id });
    } else {
      // Create new user using createPartnerUser
      const partnerUserDto: CreatePartnerUserDto = {
        user: {
          tag_user_id: null,
          tag_user_email: email,
        },
      };

      user = await this.createPartnerUser(partnerUserDto, company);

      // Update the newly created user with tag_country and marketing_opt_in
      await this.usersRepository.update(
        { id: user.id },
        {
          partner_country: country,
          marketing_opt_in: marketing_opt_in,
        }
      );
      // Fetch updated user
      user = await this.findOne({ id: user.id });
    }

    return user;
  }
}
