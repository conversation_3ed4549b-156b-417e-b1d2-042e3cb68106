import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as turf from '@turf/turf';
import { Point } from 'geojson';
// import * as geolib from 'geolib';
import { isArray, isEmpty, isNumber } from 'lodash';
import _ from 'lodash';
import { In, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { IGolfService } from 'src/igolf/igolf.service';
import { Sg } from 'src/libs/sg';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { User } from 'src/users/entities/user.entity';
import { Coordinates } from 'src/utils/concers/coordinates';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { Stats } from 'src/utils/smart-golf/stats';
import { SmartGolfStatsCalculationsBuildGreenDirections } from 'src/utils/smart-golf/stats/calculations/build_green_directions';
import { GeocoderCalculations } from 'src/utils/smart-golf/stats/calculations/geocoder';
import { SmartGolfStatsCalculationsGreenDistances } from 'src/utils/smart-golf/stats/calculations/green_distances';
import { SmartGolfStatsSandSaves } from 'src/utils/smart-golf/stats/sand_saves';
import {
  centroid,
  convertArrToLocation,
  coordsDistance,
  getBearingBetweenPoints,
  getCenterPoint,
  getDistancePoints,
  includeStr,
  isPointEmpty,
} from 'src/utils/utils';
import { StrokePlayed } from './entities/stroke-played.entity';

@Injectable()
export class StrokesPlayedService {
  private readonly logger = new Logger(StrokesPlayedService.name);
  constructor(
    @InjectRepository(StrokePlayed)
    private strokePlayedRepo: Repository<StrokePlayed>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(StrokeStat)
    private strokeStatRepo: Repository<StrokeStat>,
    @Inject(forwardRef(() => IGolfService)) private readonly igolfService: IGolfService
  ) {}
  async create(createStrokesPlayedDto: any) {
    return await this.strokePlayedRepo.save(this.strokePlayedRepo.create(createStrokesPlayedDto));
  }

  findAll() {
    return `This action returns all strokesPlayed`;
  }
  async countBy(option: any) {
    return await this.strokePlayedRepo.count(option);
  }
  async findBy(option: any) {
    return await this.strokePlayedRepo.find(option);
  }
  async findRawBy(option: any) {
    const query = this.strokePlayedRepo.createQueryBuilder('stroke');
    query.where(option.where);

    if (option.getClub) {
      option.select = [...option.select, 'clubs.club_type AS club_type', 'stroke.club_id as club_id', 'cdm_witb_id'];
      query.leftJoin(
        (subQuery) => {
          return subQuery.from(Club, 'clubs').select(['club_type', 'clubs."id" AS s_club_id', 'cdm_witb_id']);
        },
        'clubs',
        'clubs.s_club_id = stroke.club_id'
      );
    }
    if (option.getStat) {
      const statsFields = [
        'stats.stroke_played_id',
        'stats.starting_as_penalty_shot',
        'stats.starting_as_recovery_shot',
        'stats.starting_distance_to_pin',
        'stats.starting_as_difficult_shot',
        'stats.ending_as_penalty_shot',
        'stats.ending_as_difficult_shot',
        'stats.ending_as_recovery_shot',
        'stats.strokes_gained',
        'stats.strokes_gained_pro',
        'stats.strokes_gained_five',
        'stats.strokes_gained_ten',
        'stats.strokes_gained_fifteen',
        'stats.strokes_gained_twenty',
        'stats.sand_saved_opportunity',
        'stats.sand_saved',
        'stats.shot_distance',
        'stats.starting_lie',
        'stats.ending_lie',
        'stats.ends_in_hole',
        'stats.long_tee',
        'stats.holes_par',
        'stats.fairways_in_regulation',
        'stats.greens_in_regulation',
        'stats.short_tee',
        'stats.long',
        'stats.short',
        'stats.left',
        'stats.right',
        'stats.approach',
        'stats.putting',
      ];
      option.select = [...option.select, ...statsFields];
      query.leftJoin(
        (subQuery) => {
          return subQuery.from(StrokeStat, 'stats');
        },
        'stats',
        'stats.stroke_played_id = stroke.id'
      );
    }
    query.select(option.select);
    return await query.getRawMany();
  }
  async find(option: any) {
    return await this.strokePlayedRepo.find({
      ...option,
      order: { ordinal: 'ASC' },
      cache: { milliseconds: 3000 },
    });
  }

  findOne(id: number) {
    return `This action returns a #${id} strokesPlayed`;
  }

  remove(id: number) {
    return `This action removes a #${id} strokesPlayed`;
  }
  async strokes_gained_from_stats(stroke_ids, userId) {
    let userLevel = await this.getUserSkillLevel(userId);
    if (userLevel == 'pga') {
      userLevel = 'pro';
    }
    if (userLevel?.toLowerCase() == 'scratch') {
      userLevel = '';
    }
    if (isNumber(+userLevel)) {
      switch (+userLevel) {
        case 5:
          userLevel = 'five';
          break;
        case 10:
          userLevel = 'ten';
          break;
        case 15:
          userLevel = 'fifteen';
          break;
        case 20:
          userLevel = 'twenty';
          break;
      }
    }
    const base_line = isEmpty(userLevel) ? 'strokes_gained' : `strokes_gained_${userLevel}`;
    const SGQueryBuilder = this.strokeStatRepo.createQueryBuilder();
    SGQueryBuilder.where({ stroke_played_id: In(stroke_ids) });
    SGQueryBuilder.select(['id', `${base_line} AS sg`, 'stroke_played_id']);
    const strokeGained = await SGQueryBuilder.getRawMany();
    return strokeGained;
  }
  async generateFairwayPolygon(holePlayed) {
    // return null;
    const holeNumber = +holePlayed.name;
    const courseId = holePlayed.igolf_course_id;
    const points = await this.igolfService.coordinatesFor(holeNumber, 'fairway', courseId);
    if (points && points.length > 0) {
      return points;
    } else {
      return null;
    }
  }

  // #
  // # if no polygons because the shot is not inside one, then using all fairway polygons,
  // # calculate the distance to center and use the closest one.

  // #
  // # Hacky way of getting the correct polygon the shot is inside of since
  // # CAPIPolygon object does not allow accessing the points
  // #
  fairway_polygon_index() {
    // polys = self.course.convert_to_polygon( self.hole_played.name.to_i, "fairway" )
    // index = 0
    // polys.each_with_index do |p, i|
    //   index = i if p.contains?(self.next_stroke.try(:coords))
    // end
    // index
  }

  fairway_polygon_as_json() {
    //   fairway_polygon.exterior_ring.points.map do |point|
    //   { lat: point.y, lng: point.x }
    // end
  }

  async greenPolygon(holePlayed, courseId) {
    const greenPolygon = await this.igolfService.coordinatesFor(holePlayed.name, 'green', courseId);
    // this.logger.debug({ greenPolygon });
    if (greenPolygon) {
      return greenPolygon[0];
    }
    return null;
  }

  generateGetPreviousShotOrCurrent(allStrokes, stroke) {
    // return self.generate_previous_stroke(allstrokes) if self.lie =~ /green/i
    // self
    if (includeStr(stroke.lie, 'green')) {
      return this.generatePreviousStroke(allStrokes, stroke);
    }
    return stroke;
  }

  async generateBuildGreenMapping(allStrokes, holePlayed, stroke) {
    //   previous_shot_or_current = generate_get_previous_shot_or_current(allstrokes)
    //   return {} if green_polygon.blank?
    //   return {} if previous_shot_or_current.blank?
    //   return {} if previous_shot_or_current.missing_coordinates?
    //   green_direction = ::SmartGolf::Stats::Calculations::BuildGreenDirections.new(
    //     previous_shot_or_current.coords,
    //     green_polygon,
    //     holeplayed.try(:flag)
    //   )
    //  green_direction.four_green_points

    const previousShotOrCurrent = this.generateGetPreviousShotOrCurrent(allStrokes, stroke);
    if (!previousShotOrCurrent || this.missingCoordinates(previousShotOrCurrent, previousShotOrCurrent)) {
      return '';
    }
    const greenPolygons = await this.greenPolygon(holePlayed, holePlayed.igolf_course_id);
    if (!greenPolygons) {
      return '';
    }
    const green_direction: any = new SmartGolfStatsCalculationsBuildGreenDirections().getGreenEdgePoints(
      previousShotOrCurrent?.coords?.coordinates,
      greenPolygons,
      holePlayed.pin_location
    );

    return green_direction || '';
  }

  generateGetNextShotOrFlagLocation(allstrokes, holePlayed, nextStroke) {
    // nextstroke = self.generate_next_stroke(allstrokes)
    // return hole_played.flag if nextstroke.blank?
    // [ nextstroke.coords.y, nextstroke.coords.x ]
    try {
      if (!nextStroke) {
        return holePlayed?.pin_location?.coordinates;
      }
      return nextStroke?.coords?.coordinates;
    } catch (error) {
      this.logger.error(`GENERATE GET NEXT SHOT OR FLAG LOCATION`);
      this.logger.error(error.message);
      return [];
    }
  }

  async generateGetGreenDistances(allStrokes, holePlayed, nextStroke, stroke) {
    // shot_or_flag_location = generate_get_next_shot_or_flag_location(allstrokes, hole_played)
    // return {} if shot_or_flag_location.blank?
    // green = ::SmartGolf::Stats::Calculations::GreenDistances.new(
    //   generate_build_green_mapping(allstrokes, hole_played),
    //   shot_or_flag_location
    // )
    // green.get_distances
    const shotOrFlagLocation = this.generateGetNextShotOrFlagLocation(allStrokes, holePlayed, nextStroke);
    if (isPointEmpty(shotOrFlagLocation)) {
      return null;
    }
    const greenPoints = await this.generateBuildGreenMapping(allStrokes, holePlayed, stroke);
    const green = new SmartGolfStatsCalculationsGreenDistances(greenPoints, shotOrFlagLocation);
    return green.get_distances();
  }

  async generateGreenDistances(allStrokes, nextStroke, holePlayed, stroke, isTagRound = false) {
    // distances = generate_get_green_distances(allstrokes, hole_played)
    // return distances if nextstroke.blank?
    // if (nextstroke.coords.distance(pin_location) * METERS_TO_YARDS) > 30.99
    //   if distances.key?("long")
    //     distances["long"] = ( generate_stat_distance_to_pin(nextstroke, hole_played).to_f * METERS_TO_YARDS )
    //   else
    //     distances["short"] = ( generate_stat_distance_to_pin(nextstroke, hole_played).to_f * METERS_TO_YARDS )
    //   end
    // end
    // distances
    const distances = await this.generateGetGreenDistances(allStrokes, holePlayed, nextStroke, stroke);
    if (!nextStroke || isPointEmpty(nextStroke)) {
      return distances;
    }

    let distanceNextStrokeToPin =
      getDistancePoints(nextStroke.coords.coordinates, holePlayed.pin_location.coordinates) * Stats.METERS_TO_YARDS;

    if (distanceNextStrokeToPin) {
      distanceNextStrokeToPin = +distanceNextStrokeToPin.toFixed(2);
    }
    if (distanceNextStrokeToPin > 30.99) {
      const distanceKey = Object.keys(distances);
      const longShortDistance =
        +(await this.generateStatDistanceToPin(nextStroke, holePlayed, stroke, isTagRound)) * Stats.METERS_TO_YARDS;
      if (distanceKey.includes('long')) {
        distances['long'] = longShortDistance;
      } else {
        distances['short'] = longShortDistance;
      }
    }
    return distances;
  }

  async user(userId) {
    // @user ||= self.hole_played.round.user || round.user rescue nil
    const user = this.userRepo.findOneBy({ id: userId });
    return user;
  }

  user_id() {
    // user.try(:id)
  }

  facility_id() {
    // round.try(:facility_id)
  }

  tee_center_to_shot_bearing() {
    // Geocoder::Calculations.bearing_between(tee_center, to_coordinates)
  }

  tee_center_to_next_shot_bearing() {
    // return nil unless self.next_stroke.present?
    // ::Geocoder::Calculations.bearing_between(tee_center, self.next_stroke.to_coordinates)
  }

  tee_shot_ends_in_fairway(next_stroke) {
    // return [] if self.next_stroke.blank? || course.blank?
    // fairway = course_fairway_polygons.find do |polygon|
    //   polygon.contains?(self.next_stroke.coords)
    // end
    // fairway.blank? ? course_fairway_polygons[0] : fairway
    if (!next_stroke) {
      return [];
    }
    // let fairwayPolygons = this.course_fairway_polygons()
    // fairway = course_fairway_polygons.find do |polygon|
    //   polygon.contains?(self.next_stroke.coords)
    // end
    // fairway.blank? ? course_fairway_polygons[0] : fairway
  }

  // #
  // # thienbv
  // # improve tee_shot_ends_in_fairway
  // #
  generate_tee_shot_ends_in_fairway(nextstroke) {
    console.log({ nextstroke });

    // return [] if nextstroke.blank? || course.blank?
    // fairway = course_fairway_polygons.find do |polygon|
    //   polygon.contains?(nextstroke.coords)
    // end
    // fairway.blank? ? course_fairway_polygons[0] : fairway
  }

  tee_center_to_fairway_bearing() {
    // centerpoint = tee_shot_ends_in_fairway.try(:centroid)
    // return nil if centerpoint.blank?
    // ::Geocoder::Calculations.bearing_between(
    //   self.to_coordinates,
    //   [ centerpoint.y, centerpoint.x ]
    // )
  }
  // # thienbv
  // # improve tee_center_to_fairway_bearing
  // #
  generate_tee_center_to_fairway_bearing(nextstroke) {
    console.log(nextstroke);

    // centerpoint = generate_tee_shot_ends_in_fairway(nextstroke).try(:centroid)
    // return nil if centerpoint.blank?
    // ::Geocoder::Calculations.bearing_between(
    //   self.to_coordinates,
    //   [ centerpoint.y, centerpoint.x ]
    // )
  }

  tee_to_green_bearing() {
    // return 0 if self.hole_played.blank? ||
    // if self.hole_played.green_center.present?
    //   ::Geocoder::Calculations.bearing_between(
    //     tee_center,
    //     [ self.hole_played.green_center.y,
    //       self.hole_played.green_center.x ]
    //   )
    // else
    //   ::Geocoder::Calculations.bearing_between(tee_center, green_center)
    // end
  }

  tee_to_green_bearing_normalized() {
    // rotate_west_to_east( tee_to_green_bearing )
  }

  tee_center_to_fairway_bearing_normalized() {
    // rotate_west_to_east( tee_center_to_fairway_bearing )
  }

  tee_shot_to_next_shot_bearing(nextstroke) {
    console.log(nextstroke);

    // nextstroke = (nextstroke.present?) ? nextstroke : self.next_stroke
    // return nil unless nextstroke.present?
    // return nil unless self.lie =~ /tee/i
    // ::Geocoder::Calculations.bearing_between(
    //   self.to_coordinates,
    //   nextstroke.to_coordinates
    // )
  }

  tee_shot_to_next_shot_bearing_normalized(nextstroke) {
    console.log(nextstroke);

    // rotate_west_to_east( tee_shot_to_next_shot_bearing(nextstroke: nextstroke) )
  }

  get_bearing_between(point_a, point_b) {
    return GeocoderCalculations.bearingBetween(point_a, point_b);
  }
  tee_to_shot_bearing() {
    // return nil unless self.lie =~ /tee/i
    // return nil unless self.next_stroke.present?
    // @tee_to_shot_bearing ||= rotate_west_to_east(tee_center_to_next_shot_bearing)
  }

  // #
  // # 93-172 wrong side
  // # 347 wrong side
  // #
  bearing_to_dogleg() {
    // return nil if self.ordinal.to_i != 1
    // return nil if self.next_stroke.blank?
    // return nil if self.next_stroke.lie =~ /green/i
    // if tee_shot_to_next_shot_bearing_normalized.to_f >
    //     tee_to_green_bearing_normalized.to_f
    //     "left"
    // elsif tee_shot_to_next_shot_bearing_normalized.to_f <
    //     tee_to_green_bearing_normalized.to_f
    //     "right"
    // else
    //   "center"
    // end
  }

  // #
  // # thienbv
  // # improve bearign_to_dogleg
  // #
  // #
  generate_bearing_to_dogleg(nextstroke, stroke) {
    console.log({ nextstroke, stroke });

    // return nil if self.ordinal.to_i != 1
    // return nil if nextstroke.blank?
    // return nil if nextstroke.lie =~ /green/i
    // if tee_shot_to_next_shot_bearing_normalized(nextstroke: nextstroke).to_f >
    //     tee_to_green_bearing_normalized.to_f
    //     "left"
    // elsif tee_shot_to_next_shot_bearing_normalized(nextstroke: nextstroke).to_f <
    //     tee_to_green_bearing_normalized.to_f
    //     "right"
    // else
    //   "center"
    // end
  }

  // # factory access
  // #
  factory() {
    // @factory ||= ::RGeo::Geos.factory(srid: 4326)
  }

  course() {
    // round = self.round
    // if round.map_id == Round::MAP_TYPE_IGOLF
    //   @course ||= Course::Igolf.new(round.igolf_course_id, nil, round.user_id)
    // else
    //   @course ||= round.course
    // end
  }
  course_fairway_polygons(courseId, holePlayedName) {
    console.log({ courseId, holePlayedName });

    // return nil unless course
    // course.fairway_polygons(self.hole_played.name)
  }

  fairway_centerlines() {
    // @fairway_centerlines ||= ::FairwayCenterline.new(self).send(:draw_centerline)
    return {};
  }

  fairway_centerline(stroke) {
    console.log(stroke);
    // @fairway_centerline ||= ::FairwayCenterline.new(self)
    return {};
  }

  get_shot_angle_bearing(degrees = 90) {
    console.log({ degrees });

    // bearing = tee_to_shot_bearing.to_f
    // bearing = if bearing_to_dogleg == "left"
    //   bearing + degrees
    // else
    //   bearing - degrees
    // end
    // format_bearing(bearing)
  }

  fairway_with_shot_centerpoint(stroke) {
    console.log({ stroke });

    // tee_shot_ends_in_fairway.try(:centroid)
    // this.tee_shot_ends_in_fairway.try(:centroid)
  }

  // # thienbv
  // # improve fairway_with_shot_centerpoint
  // #
  generate_fairway_with_shot_centerpoint(stroke, nextstroke, holePlayed) {
    console.log({ stroke, nextstroke, holePlayed });

    // generate_tee_shot_ends_in_fairway(nextstroke).try(:centroid)
    // return this.generate_tee_shot_ends_in_fairway(nextstroke).try(:centroid)
  }

  tee_to_fairway_line() {
    // centerpoint = tee_shot_ends_in_fairway.try(:centroid)
    // return nil unless centerpoint
    // factory.line( self.coords, centerpoint )
  }

  tee_to_green_line() {
    //   center = if self.hole_played.green_center.present?
    //   self.hole_played.green_center
    // elsif green_center.present?
    //   factory.point( green_center[1], green_center[0])
    // else
    //   nil
    // end
    // if center.blank?
    //   return nil
    // end
    // factory.line( self.coords, center )
  }

  // #
  // # tee_shot_ends_in_fairway.try(:centroid)
  // #
  shot_to_fairway_line_bearing() {
    //   return 0 if tee_center_to_fairway_bearing.to_f.zero?
    //   if bearing_to_dogleg == "left"
    //    tee_center_to_fairway_bearing.to_f + 90
    //  else
    //    tee_center_to_fairway_bearing.to_f - 90
    //  end
  }

  // # thienbv
  // # improve shot_to_fairway_line_bearing
  // #
  generate_shot_to_fairway_line_bearing(nextstroke) {
    console.log({ nextstroke });

    //   tee_center_to_fwb = generate_tee_center_to_fairway_bearing(nextstroke)
    //   return 0 if tee_center_to_fwb.to_f.zero?
    //   if generate_bearing_to_dogleg(nextstroke) == "left"
    //    tee_center_to_fwb.to_f + 90
    //  else
    //    tee_center_to_fwb.to_f - 90
    //  end
  }

  generate_endpoint_for_shot_to_fairway_centerline() {
    // ::Geocoder::Calculations.endpoint(
    //   self.next_stroke.to_coordinates,
    //   shot_to_fairway_line_bearing,
    //   0.2
    // )
  }

  // # thienbv
  // # improve generate_endpoint_for_shot_to_fairway_centerline
  // #
  add_generate_endpoint_for_shot_to_fairway_centerline(nextstroke) {
    // nextstroke_to_coord = (nextstroke.present?) ? nextstroke.to_coordinates : [0, 0]
    // ::Geocoder::Calculations.endpoint(
    //   nextstroke_to_coord,
    //   generate_shot_to_fairway_line_bearing(nextstroke),
    //   0.2
    // )
    const nextstroke_to_coord = nextstroke ? this.toCoordinates(nextstroke) : [0, 0];
    return GeocoderCalculations.endpoint(
      nextstroke_to_coord,
      this.generate_shot_to_fairway_line_bearing(nextstroke),
      0.2
    );
  }

  shot_to_fairway_centerline_line() {
    // return nil if self.next_stroke.blank?
    // factory.line(
    //   self.next_stroke.coords,
    //   factory.point(
    //     generate_endpoint_for_shot_to_fairway_centerline[1],
    //     generate_endpoint_for_shot_to_fairway_centerline[0]
    //   )
    // )
  }

  // # thienbv
  // # improve shot_to_fairway_centerline_line
  // #
  generate_shot_to_fairway_centerline_line(nextstroke) {
    console.log({ nextstroke });

    // shot_to_fairway_centerline = add_generate_endpoint_for_shot_to_fairway_centerline(nextstroke)
    // return nil if nextstroke.blank?
    // factory.line(
    //   nextstroke.coords,
    //   factory.point(
    //     shot_to_fairway_centerline[1],
    //     shot_to_fairway_centerline[0]
    //   )
    // )
    // let shot_to_fairway_centerline = this.add_generate_endpoint_for_shot_to_fairway_centerline(nextstroke)
    // return nil if nextstroke.blank?
    // factory.line(
    //   nextstroke.coords,
    //   factory.point(
    //     shot_to_fairway_centerline[1],
    //     shot_to_fairway_centerline[0]
    //   )
    // )
  }

  fairway_centerpoint_same_as_shot(next_stroke, stroke) {
    // return false if self.next_stroke.blank?
    // fairway_with_shot_centerpoint == self.next_stroke.coords
    if (isPointEmpty(next_stroke)) {
      return false;
    }
    return this.fairway_with_shot_centerpoint(stroke) == next_stroke.coords;
  }

  // # thienbv
  // # improve fairway_centerpoint_same_as_shot
  // #
  generate_fairway_centerpoint_same_as_shot(stroke, nextstroke, holePlayed) {
    // return false if nextstroke.blank?
    // generate_fairway_with_shot_centerpoint(nextstroke) == nextstroke.coords
    if (!nextstroke) {
      return false;
    }
    return this.generate_fairway_with_shot_centerpoint(stroke, nextstroke, holePlayed) == nextstroke.coords.coordinates;
  }

  green_center() {
    // if self.round.map_id == Round::MAP_TYPE_IGOLF
    //     Course::Igolf.new(self.round.igolf_course_id, nil, self.round.user_id).green_center(self.hole_played.name)
    // else
    //     self.hole_played.green_center || self.round.course.green_center( self.hole_played.name )
    // end
  }

  shot_to_fairway_centerline_intersection(next_stroke, stroke) {
    console.log({ next_stroke, stroke });

    // return fairway_with_shot_centerpoint if fairway_centerpoint_same_as_shot?
    // return nil if shot_to_fairway_centerline_line.blank?
    // clean_intersection(
    //   tee_to_green_line.intersection( shot_to_fairway_centerline_line ),
    //   shot_to_fairway_centerline_line
    // )
  }

  // # thienbv
  // # improve shot_to_fairway_centerline_intersection
  // #
  generate_shot_to_fairway_centerline_intersection(stroke, nextstroke, holePlayed) {
    console.log(stroke, nextstroke, holePlayed);
    // fairway_centerline_line = generate_shot_to_fairway_centerline_line(nextstroke)
    // return generate_fairway_with_shot_centerpoint(nextstroke) if generate_fairway_centerpoint_same_as_shot?(nextstroke)
    // return nil if fairway_centerline_line.blank?
    // check_tee_to_green_line = tee_to_green_line
    // return nil if check_tee_to_green_line.blank?
    // clean_intersection(
    //     check_tee_to_green_line.intersection( fairway_centerline_line ),
    //     fairway_centerline_line
    // )
  }

  distance_between_next_stroke_and_fairway_centerline_in_miles(next_stroke, stroke) {
    console.log(next_stroke, stroke);
    // return 0.0005681818181818182 if fairway_centerpoint_same_as_shot?
    // return 0 if shot_to_fairway_centerline_intersection.blank?
    // ::Geocoder::Calculations.distance_between(
    //   self.next_stroke.to_coordinates,
    //   shot_to_fairway_centerline_intersection
    // )
    // if (this.fairway_centerpoint_same_as_shot(next_stroke)) {
    //   return 0.0005681818181818182;
    // }
    // if (this.shot_to_fairway_centerline_intersection(next_stroke, stroke)) {
    //   return 0;
    // }
    // return geolib.getDistance(
    //   this.to_coordinates(next_stroke),
    //   this.shot_to_fairway_centerline_intersection(stroke)
    // );
  }

  // # thienbv
  // # improve distance_between_next_stroke_and_fairway_centerline_in_miles
  // #
  generate_distance_between_next_stroke_and_fairway_centerline_in_miles(stroke, nextstroke, holePlayed) {
    console.log(nextstroke);

    // fairway_centerline_intersection = generate_shot_to_fairway_centerline_intersection(nextstroke)
    // return 0.0005681818181818182 if generate_fairway_centerpoint_same_as_shot?(nextstroke)
    // return 0 if fairway_centerline_intersection.blank?
    // ::Geocoder::Calculations.distance_between(
    //   nextstroke.to_coordinates,
    //   fairway_centerline_intersection
    // )
    // let fairway_centerline_intersection = this.generate_shot_to_fairway_centerline_intersection(
    //   stroke,
    //   nextstroke,
    //   holePlayed
    // );
    if (this.generate_fairway_centerpoint_same_as_shot(stroke, nextstroke, holePlayed)) {
      return 0.0005681818181818182;
    }
    // if(!fairway_centerline_intersection) {
    //   return 0;
    // }
    // GeocoderCalculations.distanceBetween(nextstroke.coords.coordinates, fairway_centerline_intersection);
  }

  shot_distance_to_fairway_centerline(next_stroke, stroke) {
    console.log(next_stroke, stroke);

    // distance_between_next_stroke_and_fairway_centerline_in_miles.to_f * ::SmartGolf::Stats::MILES_TO_YARDS
    // this.distance_between_next_stroke_and_fairway_centerline_in_miles(next_stroke, stroke) * Stats.MILES_TO_YARDS;
  }

  shot_distance_to_fairway_centerline_in_meters() {
    // distance_between_next_stroke_and_fairway_centerline_in_miles.to_f * ::SmartGolf::Stats::MILES_TO_METERS
  }

  //  # thienbv
  // # improve shot_distance_to_fairway_centerline_in_meters
  // #
  generate_shot_distance_to_fairway_centerline_in_meters(stroke, nextstroke, holePlayed) {
    // generate_distance_between_next_stroke_and_fairway_centerline_in_miles(nextstroke).to_f * ::SmartGolf::Stats::MILES_TO_METERS
    return 0;
    return (
      this.generate_distance_between_next_stroke_and_fairway_centerline_in_miles(stroke, nextstroke, holePlayed) *
      Stats.MILES_TO_METERS
    );
  }

  shot_to_fairway_centerline_distance() {
    // centerpoint = tee_shot_ends_in_fairway.try(:centroid)
    // tee_to_fairway_line = factory.line( self.coords,
    //                                     centerpoint )
    //  bearing_to_normalize = if bearing_to_dogleg == "left"
    //   tee_center_to_fairway_bearing + 90
    // else
    //   tee_center_to_fairway_bearing - 90
    // end
    // endpoint = ::Geocoder::Calculations.endpoint(
    //   self.next_stroke.to_coordinates,
    //   bearing_to_normalize,
    //   0.2
    // )
    // shot_to_fairway_line = factory.line(
    //   self.next_stroke.coords,
    //   factory.point(endpoint[1], endpoint[0])
    // )
    // intersect = clean_intersection(
    //   tee_to_fairway_line.intersection( shot_to_fairway_line ),
    //   shot_to_fairway_line
    // )
    // distance_in_miles = ::Geocoder::Calculations.distance_between(
    //   self.next_stroke.to_coordinates,
    //   intersect
    // )
    // distance_in_miles * ::SmartGolf::Stats::MILES_TO_YARDS
  }

  get_shot_line_intersections(shot_line) {
    console.log(shot_line);

    // fairway_centerlines.compact.map do |line|
    //   clean_intersection( shot_line.intersection( line ), line )
    // end.compact.reject { |i| i.blank? }
  }

  shot_to_fairway_intersect(degrees = 90) {
    console.log(degrees);

    // endpoint = ::Geocoder::Calculations.endpoint(
    //   self.next_stroke.to_coordinates, get_shot_angle_bearing(degrees),
    //   0.2
    // )
    // shot_line     = factory.line(self.next_stroke.coords, factory.point(endpoint[1], endpoint[0]))
    // return [] if shot_line.blank?
    // intersections = fairway_centerlines.compact.map do |line|
    //   clean_intersection( shot_line.intersection( line ), line )
    // end.compact.reject { |i| i.blank? }
    // intersections.respond_to?(:first) ? intersections.first : intersections
  }

  clean_intersection(intersection, line) {
    console.log(intersection, line);

    // return nil unless intersection.present?
    // # getting a line string... when we want a point
    // if intersection.respond_to?(:points)
    //   points        = intersection.points.select { |point| !line.points.include?(point) }
    //   intersection  = points.first
    // end
    // intersection = intersection.respond_to?(:first) ? intersection.first : intersection
    // if intersection.respond_to?(:y)
    //   [ intersection.y.round(8), intersection.x.round(8) ]
    // else
    //   []
    // end
  }

  // #
  // # Normalize west to east
  // #
  rotateWestToEast(bearing) {
    const a = 180 - +bearing;
    const c = a + 270;
    return (c * Math.PI) / 180;
  }
  formatBearing(bearing) {
    if (+bearing < 0) {
      bearing = bearing + 360;
    }
    if (+bearing > 360) {
      bearing = bearing - 360;
    }
    return bearing;
  }

  bearingToPin(stroke, holePlayed) {
    return this.toPinBearing(this.toCoordinates(stroke), holePlayed);
  }

  bearingTo(stroke, coords) {
    if (isPointEmpty(coords)) {
      return null;
    }
    return getBearingBetweenPoints(stroke?.coords?.coordinates, coords);
  }
  pinBearingTo(coords, hole_played) {
    if (isPointEmpty(coords)) {
      return null;
    }
    return getBearingBetweenPoints(this.pinLocation(hole_played), coords);
  }

  toPinBearing(coords, hole_played) {
    if (isPointEmpty(coords)) {
      return null;
    }
    return getBearingBetweenPoints(coords, this.pinLocation(hole_played));
  }

  compass(bearing) {
    if (0 <= bearing && bearing <= 90) {
      return 'short-right';
    }
    if (91 <= bearing && bearing <= 180) {
      return 'long-right';
    }
    if (181 <= bearing && bearing <= 270) {
      return 'long-left';
    }
    if (271 <= bearing && bearing <= 360) {
      return 'short-left';
    }
    return '';
  }

  // #
  // # Get the Bearing for:
  // #
  // # 1. stroke to pin
  // # 2. stroke to next stroke
  // # 3. Pin to next stroke
  // # 4. next stroke to Pin
  // #
  // # Calculate if the shot is left or right:
  // #
  // #   * 2. - 1.
  // #
  // # Calculate if the shot is long or short:
  // #
  // #   * 3. - 1.
  // #
  bearings() {
    // @bearings ||= [
    //   bearing_to_pin.to_f,
    //   bearing_to(self.next_stroke.try(:to_coordinates)).to_f,
    //   pin_bearing_to(self.next_stroke.try(:to_coordinates)).to_f,
    //   to_pin_bearing(self.next_stroke.try(:to_coordinates)).to_f
    // ]
  }

  // # a = StrokePlayed.find(934999) # Nick R.   Right & Long
  // # b = StrokePlayed.find(943875) # James C.  Right & Short
  long_or_short() {
    // final_bearing = bearings[3] - bearings[1]
    // direction     = get_ball_directions(final_bearing)
    // direction.first
  }

  right_or_left() {
    // final_bearing = bearings[1] - bearings[0]
    // direction     = get_ball_directions(final_bearing)
    // direction.last
  }

  landed_on_green?() {
    // shot = self.next_stroke
    // return false unless shot.present?
    // return true if shot.lie =~ /green/i
    // return true if self.last_stroke? && self.lie =~ /green/i
    // false
  }

  short_of_green?() {
    //   return false if landed_on_green?
    //  (long_or_short == 'short')
  }

  // #
  // # thienbv
  // # improve short_of_geen?
  // #
  isShortOfGreen(next_stroke, all_strokes, stroke, hole_played) {
    //  return false if generate_landed_on_green?(next_stroke, all_strokes)
    //  generate_long_or_short(next_stroke, all_strokes) == 'short'
    if (this.generateLandedOnGreen(next_stroke, all_strokes, stroke)) {
      return false;
    }

    return this.generateLongOrShort(next_stroke, all_strokes, stroke, hole_played) == 'short';
  }

  generateLandedOnGreen(nextstroke, allstrokes, stroke) {
    // shot = nextstroke
    // return false unless shot.present?
    // return true if shot.lie =~ /green/i
    // return true if (self == allstrokes.last) && self.lie =~ /green/i
    // false

    const shot = nextstroke;
    if (!shot || allstrokes.length == 0) {
      return false;
    }
    if (shot?.lie?.toLowerCase().includes('green')) {
      return true;
    }
    if (shot.id == allstrokes[allstrokes.length - 1].id && stroke?.lie?.toLowerCase()?.includes('green')) {
      return true;
    }
    return false;
  }

  generateLongOrShort(nextstroke, allstrokes, stroke, hole_played) {
    // get_bearings = generate_bearings(nextstroke)
    // final_bearing = get_bearings[3] - get_bearings[1]
    // direction     = get_ball_directions(final_bearing)
    // direction.first

    const get_bearings: any = this.generateBearings(nextstroke, hole_played, stroke);
    this.logger.log({ get_bearings });

    const final_bearing = get_bearings[3] - get_bearings[1];
    const direction = this.getBallDirections(final_bearing);
    return direction[0];
  }

  generateBearings(nextstroke, hole_played, stroke) {
    // coordinates_next_stroke = nextstroke.try(:to_coordinates)
    //  @bearings ||= [
    //   bearing_to_pin.to_f,
    //   bearing_to(coordinates_next_stroke).to_f,
    //   pin_bearing_to(coordinates_next_stroke).to_f,
    //   to_pin_bearing(coordinates_next_stroke).to_f
    // ]

    const coordinates_next_stroke = this.toCoordinates(nextstroke);
    const bearings = [
      this.bearingToPin(stroke, hole_played),
      this.bearingTo(stroke, coordinates_next_stroke),
      this.pinBearingTo(coordinates_next_stroke, hole_played),
      this.toPinBearing(coordinates_next_stroke, hole_played),
    ];
    return bearings;
  }

  long_of_green?() {
    // return false if landed_on_green?
    // (long_or_short == 'long')
  }

  // #
  // # thienbv
  // # improve long_of_green?
  // #
  isLongOfGreen(next_stroke, all_strokes, stroke, hole_played) {
    // return false if generate_landed_on_green?(next_stroke, all_strokes)
    // generate_long_or_short(next_stroke, all_strokes) == 'long'
    if (this.generateLandedOnGreen(next_stroke, all_strokes, stroke)) {
      return false;
    }

    return this.generateLongOrShort(next_stroke, all_strokes, stroke, hole_played) == 'long';
  }

  isRightOfCenter(next_stroke, all_strokes, stroke, hole_played) {
    // return false if generate_landed_on_green?(next_stroke, all_strokes)
    // generate_right_or_left(next_stroke) == 'right'
    if (this.generateLandedOnGreen(next_stroke, all_strokes, stroke)) {
      return false;
    }

    return this.generateRightOrLeft(next_stroke, hole_played, stroke) == 'right';
  }

  generateRightOrLeft(nextstroke, hole_played, stroke) {
    // get_bearings = generate_bearings(nextstroke)
    // final_bearing = get_bearings[1] - get_bearings[0]
    // direction     = get_ball_directions(final_bearing)
    // direction.last

    const get_bearings: any = this.generateBearings(nextstroke, hole_played, stroke);
    let final_bearing = +get_bearings[1] - +get_bearings[0];
    if (isNaN(final_bearing)) {
      final_bearing = 0;
    }
    const direction = this.getBallDirections(final_bearing);
    return direction.pop() || 0;
  }

  isLeftOfCenter(next_stroke, all_strokes, stroke, hole_played) {
    // return false if generate_landed_on_green?(next_stroke, all_strokes)
    // generate_right_or_left(next_stroke) == 'left'
    if (this.generateLandedOnGreen(next_stroke, all_strokes, stroke)) {
      return false;
    }
    return this.generateRightOrLeft(next_stroke, hole_played, stroke) == 'left';
  }

  getBallDirections(final_bearing) {
    // c   = final_bearing.round
    // c   = (c + 360) if c < 0
    // c   = (c - 360) if c > 360
    // compass(c).split('-')

    let c = final_bearing;
    if (c < 0) {
      c = c + 360;
    }
    if (c > 360) {
      c = c - 360;
    }
    return this.compass(c).split('-');
  }
  // # For Green Left/Right/Long/Short

  angleDifference(stroke) {
    // return if self.shot_azimuth.blank? || self.result_angle_from_pin.blank?
    // diff = self.shot_azimuth - self.result_angle_from_pin
    // if diff > Math::PI
    //   diff - 2 * Math::PI
    // elsif diff < -(Math::PI)
    //   diff + 2 * Math::PI
    // else
    //   diff
    // end
    if (!stroke.shot_azimuth || !stroke.result_angle_from_pin) {
      return 0;
    }
    const diff = +stroke.shot_azimuth - +stroke.result_angle_from_pin;
    if (diff > Math.PI) {
      return diff - 2 * Math.PI;
    } else if (diff < -Math.PI) {
      return diff + 2 * Math.PI;
    } else {
      return diff;
    }
  }

  // # For Green Left/Right/Long/Short
  x(stroke) {
    // angle = Math.sin(angle_difference.to_f)
    // dist  = self.result_from_pin.to_f * 3
    // -(dist * angle * 1.09361)
    const angle = Math.sin(this.angleDifference(stroke));
    const dist = stroke.result_from_pin * 3;
    if (isNaN(dist)) {
      return 0;
    }
    return -(dist * angle * Stats.METERS_TO_YARDS);
  }

  // # For Green Left/Right/Long/Short
  y(stroke) {
    // # angle = Math.sin(angle_difference.to_f)
    // angle = Math.cos(angle_difference.to_f)
    // dist      = self.result_from_pin.to_f * 3
    // (dist * angle * 1.09361)
    const angle = Math.cos(this.angleDifference(stroke));
    const dist = stroke.result_from_pin * 3;
    if (isNaN(dist)) {
      return 0;
    }
    return dist * angle * Stats.METERS_TO_YARDS;
  }

  // # stroke xy
  xy(stroke) {
    // angle_difference_xy = angle_difference
    // dist = self.result_from_pin.to_f * 3
    // #calcular x
    // angle_x = Math.sin(angle_difference_xy.to_f)
    // x = -(dist * angle_x * 1.09361)
    // #calcular y
    // angle_y = Math.cos(angle_difference_xy.to_f)
    // y = (dist * angle_y * 1.09361)
    // [x, y]

    const angleDifferenceXY = this.angleDifference(stroke);
    const dist = stroke.result_from_pin * 3 || 0;
    // #calcular x
    const angleX = Math.sin(+angleDifferenceXY);
    let x = -(dist * angleX * Stats.METERS_TO_YARDS);
    // #calcular y
    const angleY = Math.cos(+angleDifferenceXY);
    let y = dist * angleY * Stats.METERS_TO_YARDS;
    if (isNaN(x)) {
      x = 0;
    }
    if (isNaN(y)) {
      y = 0;
    }
    return [x, y];
  }

  to_graph(stroke) {
    console.log(stroke);

    // {
    //   x:                  self.x,
    //   y:                  self.y,
    //   color:              self.landed_on_green? ? 'green' : 'red',
    //   bulletBorderColor:  self.landed_on_green? ? 'green' : 'red',
    //   start_lie:          self.lie,
    //   end_lie:            self.end_lie,
    //   distance_to_pin:    self.distance_to_pin,
    //   end_to_pin:         (self.next_stroke.try(:distance_to_pin) || 0),
    //   stroke_id:          self.id,
    // }
  }

  resetup() {
    // self.hole_played.round.update_stats!
    // self.reload
  }

  update_ordinals() {
    // all_strokes   = hole_played.strokes_played.order("ordinal asc, created_at asc")
    // all_strokes.each_with_index do |stroke, i|
    //   stroke.update_attribute :ordinal, i+1
    // end
  }

  mark_as_difficult() {
    // self.unmark_penalty!
    // self.unmark_recovery!
    // self.difficult? ? unmark_difficult! : mark_difficult!
  }
  mark_as_penalty() {
    // self.unmark_difficult!
    // self.unmark_recovery!
    // self.penalty? ? unmark_penalty! : mark_penalty!
  }

  mark_as_recovery() {
    // self.unmark_penalty!
    // self.unmark_difficult!
    // self.recovery? ? unmark_recovery! : mark_recovery!
  }

  mark_penalty() {
    // self.update_attribute(:penalty, true)
  }

  unmark_penalty() {
    // self.update_attribute(:penalty, false)
  }

  penalty(stroke) {
    return stroke?.penalty == true;
    // !!self.penalty
  }
  mark_recovery() {
    // self.update_attribute(:recovery, true)
  }

  unmark_recovery() {
    // self.update_attribute(:recovery, false)
  }
  recovery(stroke) {
    return stroke?.recovery == true;
  }

  mark_difficult() {
    // self.update_attribute(:difficult, true)
  }
  unmark_difficult() {
    // self.update_attribute(:difficult, false)
  }

  difficult(stroke) {
    // !!self.difficult
    return stroke?.difficult == true;
  }

  isLongTee(allStrokes: any, holePlayed: any, stroke: any) {
    // return false unless hole_played.present?
    // !!(self.hole_played.par.to_i >= 4  && ( (self == all_strokes.first) || self.lie =~ /tee/i ))
    if (!holePlayed || allStrokes.length == 0) {
      return false;
    }
    return +holePlayed.par >= 4 && (allStrokes[0].id == stroke.id || includeStr(this.lie(stroke), 'tee'));
  }

  isApproach(stroke, hole_played) {
    try {
      if (this.penalty(stroke) || this.recovery(stroke)) {
        return false;
      }
      if (includeStr(this.lie(stroke), 'tee')) {
        return false;
      }
      const distanceToPin = getDistancePoints(stroke?.coords?.coordinates, this.pinLocation(hole_played));
      if (distanceToPin < 100 * Stats.YARDS_TO_METERS) {
        return false;
      }
      return true;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  }

  isShort(stroke, hole_played) {
    try {
      if (this.penalty(stroke) || this.recovery(stroke)) {
        return false;
      }
      if (includeStr(this.lie(stroke), 'green')) {
        return false;
      }
      const distanceToPin = getDistancePoints(stroke.coords?.coordinates, this.pinLocation(hole_played));
      if (distanceToPin >= 100 * Stats.YARDS_TO_METERS) {
        return false;
      }
      return true;
    } catch (error) {
      this.logger.error(error.message);
      return false;
    }
  }

  // ########################################################################################
  // # Putting
  // #
  // # ==== About
  // #
  // # Starts on the green.
  // #
  putting(stroke) {
    // !!(self.lie =~ /green/i)
    return stroke?.lie?.toLowerCase().includes('green');
  }

  isGreensInRegulation(allStrokes, holePlayed, stroke) {
    // hole_played.is_greens_in_regulation?(self.id)
    if (!holePlayed.par) {
      return false;
    }
    let isGreenInRegulation = false;
    const shotNumber = +holePlayed.par - 2;
    const totalStrokes = allStrokes.length;
    try {
      if (
        totalStrokes == 1 ||
        (totalStrokes <= shotNumber && allStrokes[totalStrokes - 2].id == stroke.id) ||
        (includeStr(allStrokes[shotNumber]?.lie, 'green') && allStrokes[shotNumber - 1].id == stroke.id)
      ) {
        isGreenInRegulation = true;
      }
      return isGreenInRegulation;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  }

  // #
  // # Fairways in Regulation
  // #
  // # == Requirements
  // #
  // #   * Must not be a penalty shot
  // #   * Must be on a hole with a par 4 or greater
  // #   * Must land on either the fairway or green
  // #
  generateFairwaysInRegulation(nextStroke, holePlayed) {
    if (!nextStroke || nextStroke.penalty == true || +holePlayed.par >= 4) {
      return false;
    }
    const genEndline = this.generateEndLie(nextStroke);
    return ['fairway', 'green'].includes(genEndline?.toLowerCase().trim());
  }

  generateSandSave(stroke, allStroke, holePlayed) {
    const smGolfSandSave = new SmartGolfStatsSandSaves([stroke], [...allStroke], holePlayed);
    const savedOpportunity = smGolfSandSave.numberOfSandSavedOpportunities();
    const saved = smGolfSandSave.numberOfSandsSaved();
    return { opportunity: savedOpportunity, saved: saved };
  }

  endLie(allStrokes, stroke) {
    return this.generateNextStroke(allStrokes, stroke)?.lie || 'green';
  }

  generateEndLie(nextStroke) {
    return nextStroke?.lie || 'green';
  }

  missingCoordinates(next_stroke, stroke) {
    const isCoordMissing = Coordinates.coordinatesMissing(stroke?.coords?.coordinates);

    if (isCoordMissing) {
      // self.meta[:missing_coordinates] = true
      stroke['missing_coordinates'] = true;
      return true;
    } else {
      return false;
    }
  }

  lie(stroke) {
    // lie_condition = read_attribute(:lie)
    // case lie_condition.to_s
    // when 'Hole Boundary'      then 'Rough'
    // when 'Facility Boundary'  then 'Rough'
    // when 'Teebox Boundary'    then 'Tee'
    // when 'Trees'              then self.set_lie!
    // else
    //   lie_condition
    // end

    const lie_condition = stroke?.lie?.toLowerCase()?.trim();
    switch (lie_condition) {
      case 'hole boundary':
      case 'facility boundary':
        return 'rough';
      case 'teebox boundary':
        return 'tee';
      case 'trees':
        return this.set_lie(stroke);
      default:
        return lie_condition;
    }
  }

  lie_write_attribute(lie_condition) {
    console.log(lie_condition);

    // write_attribute(:lie, lie_condition)
  }

  green?() {
    // self.lie =~ /green/i
  }

  fairway?() {
    // self.lie =~ /fairway/i
  }
  isLastStroke(all_strokes, stroke) {
    // self == all_strokes.last
    // console.log({ all_strokes });

    return all_strokes[all_strokes.length - 1].id == stroke.id;
  }
  isFirstStroke(all_strokes, stroke) {
    // self == all_strokes.first
    if (all_strokes.length == 0) {
      return false;
    }
    return all_strokes[0].id == stroke.id;
  }

  last_stroke() {
    // @last_stroke ||= all_strokes.last
  }

  first_stroke() {
    // @first_stroke ||= all_strokes.first
  }

  nextStroke(allstrokes, stroke) {
    if (!allstrokes || allstrokes.length <= 1) {
      return null;
    }
    const indx = allstrokes.findIndex((s: any) => +s.id == +stroke.id);
    indx.to_i >= allstrokes.length - 1 ? null : allstrokes[indx.to_i + 1];
  }

  generateNextStroke(allstrokes: any, stroke: any) {
    const indx = allstrokes.findIndex((s: any) => +s.id == +stroke.id);
    const next = indx >= allstrokes.length - 1 ? null : allstrokes[indx + 1];
    return next;
  }

  previousStroke(all_strokes, stroke) {
    const indx = all_strokes.findIndex((s: any) => +s.id == +stroke.id);
    return indx <= 0 ? null : all_strokes[indx - 1];
  }

  generatePreviousStroke(allStrokes, stroke) {
    const indx = allStrokes.findIndex((s: any) => +s.id == +stroke.id);
    return indx <= 0 ? null : allStrokes[indx - 1];
  }

  all_strokes() {
    // return [] if self.hole_played.blank?
    // @all_strokes ||= self.hole_played.strokes_played.order("ordinal asc") || []
    // @all_strokes
  }

  pinLocation(hole_played) {
    return hole_played?.pin_location?.coordinates || [0, 0];
    // @pin_location ||= self.hole_played.flag_location rescue []
  }

  distance_to_pin_in_meters2() {
    // if self.lie =~ /tee/i && self.hole_played.par.to_i > 3
    //   stat_total_dogleg_hole_distance
    // else
    //   self.coords.distance(pin_location)
    // end
  }

  distance_to_pin() {
    // return 0 if missing_coordinates? || missing_pin_location?
    // distance = stat_distance_to_pin * METERS_TO_YARDS
    // self.lie =~ /green/i ? (distance * 3) : distance
  }
  distance_to_pin_in_feet() {
    // return 0 if missing_coordinates? || missing_pin_location?
    // ( distance_to_pin_in_yards * 3 )
  }

  end_distance_to_pin() {
    // return 0 if self.next_stroke.blank? || self.next_stroke.missing_coordinates? || self.next_stroke.missing_pin_location?
    // distance = self.next_stroke.distance_to_pin_in_yards
    // self.next_stroke.lie =~ /green/i ? (distance * 3) : distance
  }

  end_distance_to_pin_in_meters() {
    // return 0 if self.next_stroke.blank? || self.next_stroke.missing_coordinates? || self.next_stroke.missing_pin_location?
    // self.next_stroke.distance_to_pin_in_meters
  }

  distance_to_pin_in_yards() {
    // return 0 if missing_coordinates? || missing_pin_location?
    // stat_distance_to_pin * METERS_TO_YARDS
  }

  stat_distance_to_pin_in_yards() {
    // return 0 if missing_coordinates? || missing_pin_location?
    // stat_distance_to_pin * METERS_TO_YARDS
  }

  stat_green_in_regulation?() {
    // return false if self.hole_played.par.blank?
    // shot_number = self.hole_played.par.to_i - 2
    // all_strokes[shot_number].try(:lie) =~ /green/i
  }
  statNextStrokePenalty(next_stroke) {
    if (!next_stroke) {
      return false;
    }
    return this.penalty(next_stroke);
  }

  isStatNextStrokePenalty(next_stroke) {
    if (!next_stroke) {
      return false;
    }
    return this.penalty(next_stroke);
  }

  isStatNextStrokeRecovery(next_stroke) {
    if (!next_stroke) {
      return false;
    }
    return this.recovery(next_stroke);
  }

  isStatNextStrokeDifficult(next_stroke) {
    // return false if next_stroke.try(:difficult?).nil?
    // !!next_stroke.try(:difficult?)
    return this.difficult(next_stroke);
  }

  stat_dogleg_full_distance() {
    // start_run = Time.now
    // centerpoint = (self.fairway_polygon) ? [self.fairway_polygon.centroid.y, self.fairway_polygon.centroid.x] : nil
    //   #::FairwayCenterline.new(self).try(:fairway_center)
    // if self.lie =~ /tee/i && centerpoint
    //   # dogleg_point    = hole_played.get_dogleg_point
    //   # return unless dogleg_point.present?
    //   # doglegPoint = factory.point(dogleg_point[1], dogleg_point[0])
    //   pinLocation = [pin_location.y, pin_location.x]
    //   doglegPoint = factory.point(centerpoint[1], centerpoint[0])
    //   distance        = self.coords.distance(doglegPoint)
    //   later_distance  = ::Geocoder::Calculations.distance_between(
    //     centerpoint, pinLocation
    //   )
    //   distance + ( later_distance * MILES_TO_METERS )
    // else
    //   0
    // end
  }

  async generateStatDoglegFullDistance(nextStroke, holePlayed, stroke) {
    this.logger.debug(`GENERATE STAT DOGLEG FULL DISTANCE...`);
    const genFairwayPolygon: any = await this.generateFairwayPolygon(holePlayed);

    let centerpoint: any = null;
    const coords = nextStroke?.coords?.coordinates || stroke?.coords?.coordinates;

    centerpoint = this.getCenterPoint(genFairwayPolygon, coords, centerpoint);

    if (includeStr(this.lie(stroke), 'tee') && centerpoint) {
      const pinLocation = this.pinLocation(holePlayed);
      const distance = getDistancePoints(stroke?.coords?.coordinates, centerpoint);
      const later_distance = getDistancePoints(centerpoint, pinLocation);

      return distance + later_distance;
    } else {
      return 0;
    }
  }
  private getCenterPoint(gen_fairway_polygon: any, coords: any, centerpoint: any) {
    if (gen_fairway_polygon) {
      // if (gen_fairway_polygon.length > 1) {
      //   gen_fairway_polygon = gen_fairway_polygon[0];
      // }
      let fairwayPolygon = this.findFairwayPolygon(gen_fairway_polygon, coords, false);
      if (fairwayPolygon == null) {
        fairwayPolygon = this.findFairwayPolygon(gen_fairway_polygon, coords, true);
      }
      // const point: any = geolib.getCenter(fairwayPolygon);
      // centerpoint = [point.longitude, point.latitude];
      centerpoint = centroid(fairwayPolygon);
    }
    return centerpoint;
  }

  private findFairwayPolygon(gen_fairway_polygon: any, coords: any, forceSelect?: boolean) {
    for (const item of gen_fairway_polygon) {
      const points = item?.map((p) => {
        const point = convertArrToLocation(p);
        return [point.lon, point.lat];
      });
      if (forceSelect) {
        return points;
      }
      const arrPolygonValid = item.some(
        (polygon) => +polygon[0].toFixed(3) == +coords[0].toFixed(3) && +polygon[1].toFixed(3) == +coords[1].toFixed(3)
      );
      if (arrPolygonValid) {
        return points;
      }
    }
    return null;
  }

  async generateStatDistanceToPin(nextstroke, holeplayed, stroke, isTagRound = false) {
    this.logger.log(`GENERATE STAT DISTANCE TO PIN...`);
    if (this.missingCoordinates(stroke, stroke) || this.missingPinLocation(holeplayed)) {
      this.logger.log(`===================== STROKE MISSING COORDINATES ============================`);
      return 0;
    }

    if (includeStr(this.lie(stroke), 'tee') && +holeplayed.par > 3) {
      if (isTagRound && holeplayed.yards) {
        return holeplayed.yards * Stats.YARDS_TO_METERS;
      }
      return await this.generateStatDoglegFullDistance(nextstroke, holeplayed, stroke);
    } else {
      return getDistancePoints(stroke?.coords?.coordinates, this.pinLocation(holeplayed));
    }
  }

  statEndingDistanceToPin(all_strokes, stroke, hole_played) {
    // return 0 if self.last_stroke? || self.next_stroke.blank?
    // return 0 if self.next_stroke.coords.nil?
    // self.next_stroke.coords.distance(pin_location)
    const nextStroke = this.nextStroke(all_strokes, stroke);
    const isLastStroke = this.isLastStroke(all_strokes, stroke);
    if (isLastStroke || !nextStroke) {
      return 0;
    }
    if (this.missingCoordinates(nextStroke, nextStroke)) {
      return 0;
    }
    return coordsDistance(nextStroke.coords.coordinates, this.pinLocation(hole_played)) * Stats.MILES_TO_METERS;
    // self.next_stroke.coords.distance(pin_location)
  }

  generateStatEndingDistanceToPin(nextstroke, allstrokes, stroke, hole_played) {
    // return 0 if (self == allstrokes.last) || nextstroke.blank?
    // nextstroke.coords.distance(pin_location)
    if (!nextstroke) {
      return 0;
    }
    return getDistancePoints(nextstroke?.coords?.coordinates, this.pinLocation(hole_played));
  }

  stat_distance_to_dogleg(stroke) {
    console.log(stroke);

    // return 0 unless dogleg_point.present?
    // self.coords.distance(dogleg_point)
    return 0;
  }

  stat_dogleg_to_pin_distance(stroke) {
    console.log(stroke);

    // return 0 unless dogleg_point.present?
    // dogleg_point.distance(pin_location)
    return 0;
  }

  stat_total_dogleg_hole_distance(stroke) {
    console.log(stroke);

    // return 0 unless self.round.present? && self.round.course.present?
    // self.round.course.dogleg_distance_for( self.hole_played.name.to_i )
    return 0;
  }

  async stat_distance_shot(allstrokes, stroke, hole_played) {
    const next_stroke = this.nextStroke(allstrokes, stroke);
    if (this.missingCoordinates(stroke, stroke)) {
      return 0;
    }
    if (!next_stroke || this.missingCoordinates(next_stroke, next_stroke)) {
      return await this.generateStatDistanceToPin(next_stroke, hole_played, stroke);
    }
    return getDistancePoints(stroke.coords.coordinates, next_stroke.coords.coordinates);
    // self.coords.distance(next_stroke.coords)
  }

  async generateStatDistanceShot(nextstroke, holeplayed, stroke, isTagRound = false) {
    // return 0                                if missing_coordinates?
    // return self.generate_stat_distance_to_pin(nextstroke, holeplayed) if nextstroke.blank? || nextstroke.missing_coordinates?
    // self.coords.distance(nextstroke.coords)

    if (this.missingCoordinates(stroke, stroke)) {
      console.log(`==================missing_coordinates==========================`);
      console.log(`stroke: ${stroke?.coords?.coordinates}`);
      return 0;
    }
    if (!nextstroke || this.missingCoordinates(nextstroke, nextstroke)) {
      return await this.generateStatDistanceToPin(nextstroke, holeplayed, stroke, isTagRound);
    }
    return getDistancePoints(stroke?.coords?.coordinates, nextstroke?.coords?.coordinates);
  }

  stat_strokes_gained() {
    // sg
  }

  distance_to_dogleg() {
    // return 0 unless self.lie =~ /tee/i && dogleg_point.present?
    // @distance_to_dogleg ||= self.coords.distance(dogleg_point)
  }
  dogleg_to_pin() {
    // dogleg_point.distance(pin_location)
  }

  total_dogleg_distance() {
    // stat_total_dogleg_hole_distance
  }

  distanceToPinInMeters(stroke, holePlayed) {
    if (this.missingCoordinates(stroke, stroke) || this.missingPinLocation(holePlayed)) {
      return 0;
    }

    const meters = getDistancePoints(stroke.coords.coordinates, this.pinLocation(holePlayed)); // * Stats.MILES_TO_METERS;
    return +meters.toFixed(2);
  }

  distanceToPinInYardsWithoutDogleg(stroke, hole_played) {
    // return 0 if self.missing_coordinates?
    // meters = self.coords.distance(pin_location)
    // (meters * METERS_TO_YARDS).round(2)
    if (this.missingCoordinates(stroke, stroke) || this.missingPinLocation(hole_played)) return 0;
    const meters = coordsDistance(stroke.coords.coordinates, this.pinLocation(hole_played));
    return +(meters * Stats.METERS_TO_YARDS).toFixed(2);
  }

  dogleg_point() {
    // return nil unless hole
    // hole.has_dogleg? ? spherical_factory.point(hole.dog_leg.last, hole.dog_leg.first) : nil
  }
  // #
  // # Distance shot in meters
  // #
  distance_shot() {
    // next_stroke = self.next_stroke
    // return 0                                if missing_coordinates?
    // return self.distance_to_pin_in_meters   if next_stroke.blank? || next_stroke.missing_coordinates?
    // self.coords.distance(next_stroke.coords)
  }

  distance_shot_in_yards_or_feet() {
    // return distance_shot if distance_shot.zero?
    // dist = distance_shot * METERS_TO_YARDS
    // self.lie =~ /green/i  ? (dist * 3) : dist
  }

  ending_distance_to_pin() {
    // if next_shot = self.next_stroke
    //   meters = next_shot.coords.distance(self.hole_played.flag_location)
    //   (meters * METERS_TO_YARDS).round(2)
    // end
  }
  distanceBetween(lat1, lon1, lat2, lon2) {
    const point1 = convertArrToLocation([lat1, lon1]);
    const point2 = convertArrToLocation([lat2, lon2]);
    return GeocoderCalculations.distanceBetween(point1, point2);
    // pt1   = spherical_factory.point(lon1, lat1)
    // pt2   = spherical_factory.point(lon2, lat2)
    // pt1.distance(pt2) * METERS_TO_YARDS
  }

  within_tee_boundary?() {
    // boxes = round.course.convert_to_polygon( ordinal, "tee-boundary")
    // boxes.any?{|b| b.contains?( self.coords )}
  }

  toCoordinates(stroke) {
    // return nil if self.coords.blank?
    // convert_to_array(self.coords)
    if (!stroke) {
      return null;
    }
    // return new Coordinates().convert_to_array(stroke?.coords?.coordinates);
    return stroke?.coords?.coordinates;
  }

  hole() {
    // @hole ||= self.hole_played.hole
  }

  hole_features() {
    // @hole_features ||= hole_polygons.map(&:as_feature_without_lines).compact
  }

  get_point_for(label) {
    console.log(label);

    // point = find_first_for(label)
    // point.geometry.centroid rescue []
  }

  get_point_for_close_to_pin() {
    // green         = find_first_for("Green")
    // edge          = green.geometry.exterior_ring.points[0]
    // center        = green.geometry.centroid
    // mid_x         = ((edge.x + center.x) / 2)
    // mid_y         = ((edge.y + center.y) / 2)
    // factory.point(mid_x, mid_y)
  }

  find_first_for(label) {
    console.log(label);

    // hole_features.find do |f|
    //   f.properties.fetch('type', {}).fetch('label', nil) =~ /#{label}/i
    // end
  }

  missingPinLocation(hole_played) {
    // pin_location_missing?(pin_location)
    // pin_location_missing?(this.pin_location(hole_played))
    return isPointEmpty(this.pinLocation(hole_played));
  }

  update_scores() {
    // return if self.hole_played.shots_removed.to_i.zero?
    // self.hole_played.decrement!(:shots_removed)
  }

  set_lie(stroke) {
    // self.lie ||= find_lie
    return stroke.lie || this.find_lie(stroke);
  }

  find_lie(stroke) {
    console.log(stroke);
    // return "" if self.round.blank? || self.round.course.blank?
    // self.round.course.find_lie(self.hole_played.name, self.coords)
    return '';
  }

  set_lie_update_attribute() {
    // self.update_attribute(:lie, find_lie)
  }

  assign_coordinates() {
    //   if self.geometry.present?
    //   self.coords = factory.point(self.geometry[0].to_f, self.geometry[1].to_f)
    // end
  }

  coords_to_rgeo() {
    // convert_to_rgeo(self.coords)
  }

  get_strokes_gained() {
    // @get_strokes_gained ||= sg
  }

  async getSg(optionsSg: any) {
    const { skill_level, next_stroke, stroke, user_skill_level, shotsDistance } = optionsSg;
    const options = this.sgOptionsAllSkill(stroke, next_stroke, user_skill_level, shotsDistance);
    options['skill_level'] = skill_level;
    return await new Sg().calculate(options);
  }

  async getStartSg(optionsSg: any) {
    const { skill_level, stroke, shotsDistance } = optionsSg;
    const options = {
      skill_level,
      start_lie: this.lie(stroke)?.toString()?.toLowerCase(),
      start_distance: shotsDistance.starting_distance_to_pin,
    };
    return await new Sg().startSg(options);
  }

  async getEndSg(optionsSg: any) {
    const { skill_level, shotsDistance, next_stroke } = optionsSg;
    const options = {
      skill_level,
      end_lie: this.lie(next_stroke)?.toString()?.toLowerCase() || 'green',
      end_distance: shotsDistance.ending_distance_to_pin,
    };
    return await new Sg().endSg(options);
  }

  async getUserSkillLevel(user_id) {
    const user = await this.userRepo.findOne({
      where: { id: user_id },
      select: ['strokes_gained_baseline'],
    });
    let user_skill_level = '';

    if (!user) {
      user_skill_level = 'scratch';
    } else {
      user_skill_level = !user.strokes_gained_baseline ? 'scratch' : user.strokes_gained_baseline;
    }

    user_skill_level = user_skill_level.includes('scratch') ? '' : user_skill_level;
    user_skill_level = user_skill_level.includes('pga') ? 'pga' : user_skill_level;
    user_skill_level = user_skill_level.includes('pro') ? 'pga' : user_skill_level;
    return user_skill_level;
  }

  async strokesGainedFromStats(user_id, stroke_played_id) {
    // user_level = get_user_skill_level
    // user_level = "pro" if user_level =~/pga/
    // base_line = (user_level.empty?)? "strokes_gained" : "strokes_gained_#{user_level}"
    // @get_strokes_gained = StrokeStat.where(stroke_played_id: self.id).select("id, #{base_line} as sg").first
    // @get_strokes_gained.try(:sg)
    let user_level = await this.getUserSkillLevel(user_id);
    if (user_level?.toLowerCase().includes('pga')) {
      user_level = 'pro';
    }

    const base_line: any = isEmpty(user_level) ? 'strokes_gained' : `strokes_gained_${user_level}`;
    const strokeStatQueryBuilder = this.strokeStatRepo.createQueryBuilder();
    strokeStatQueryBuilder.where({ stroke_played_id: stroke_played_id });
    strokeStatQueryBuilder.select(['id', `${base_line} as sg`]);

    const get_strokes_gained = await strokeStatQueryBuilder.getRawMany();
    if (get_strokes_gained) {
      return get_strokes_gained[0]['sg'];
    }
    return null;
  }

  async sgOptions(next_stroke, stroke, user_id, all_strokes, hole_played) {
    const user_skill_level = await this.getUserSkillLevel(user_id);
    // let next_stroke_sg = this.next_stroke_for_sg(next_stroke, all_strokes);
    return {
      skill_level: user_skill_level,
      starts_penalty: this.penalty(stroke), // # shot did not start as a penalty
      ends_penalty: this.statNextStrokePenalty(next_stroke), //# shot did not end as a penalty
      difficult: this.difficult(stroke), // # was the shot a difficult shot
      starts_recovery: this.recovery(stroke), //# was the shot a recovery shot
      ends_recovery: this.isStatNextStrokeRecovery(next_stroke), //   # shot ends as a recovery
      start_lie: stroke.lie, //# shot started on a Tee lie condition
      end_lie: stroke.end_lie, //# shot ended on a Rough lie condition
      start_distance: this.generateStatDistanceToPin(next_stroke, hole_played, stroke), //# shot started 380.95 yds away from pin
      end_distance: this.statEndingDistanceToPin(all_strokes, stroke, hole_played), //# shot ended 148.19 yds away from pin
    };
  }
  sgOptionsAllSkill(stroke, nextstroke, user_skill_level, shotsDistance) {
    return {
      skill_level: user_skill_level,
      difficult: this.difficult(stroke), //# was the shot a difficult shot
      starts_penalty: this.penalty(stroke), //# shot did not start as a penalty
      ends_penalty: this.statNextStrokePenalty(nextstroke), //# shot did not end as a penalty
      starts_recovery: this.recovery(stroke), //# was the shot a recovery shot
      ends_recovery: this.isStatNextStrokeRecovery(nextstroke), //# shot ends as a recovery
      start_lie: stroke.lie, //# shot started on a Tee lie condition
      end_lie: this.generateEndLie(nextstroke), //# shot ended on a Rough lie condition
      start_distance: shotsDistance.starting_distance_to_pin, //# shot started 380.95 yds away from pin
      end_distance: shotsDistance.ending_distance_to_pin, //# shot ended 148.19 yds away from pin
    };
  }

  async sgOptionsStart(next_stroke, hole_played, stroke) {
    return {
      skill_level: '',
      start_lie: this.lie(stroke)?.toString()?.toLowerCase(),
      start_distance: await this.generateStatDistanceToPin(next_stroke, hole_played, stroke),
    };
  }

  sgOptionsEnd(next_stroke, hole_played, stroke, all_strokes) {
    return {
      skill_level: '',
      end_lie: this.endLie(all_strokes, stroke).toLowerCase(),
      end_distance: this.generateStatEndingDistanceToPin(next_stroke, all_strokes, stroke, hole_played),
    };
  }

  nextShotIsaPenalty(next_stroke) {
    // !!self.next_stroke.try(:penalty)
    return next_stroke?.penalty == true;
  }

  nextShotIsaHazard(next_stroke) {
    // !!(self.next_stroke.try(:lie) =~ /hazard/i)
    return next_stroke?.lie?.toLowerCase().includes('hazard');
  }

  treatNextShotasPenalty() {
    // next_shot_is_a_penalty? || next_shot_is_a_hazard?
  }

  nextStrokeForSg(next_stroke, all_strokes) {
    if (this.nextShotIsaPenalty(next_stroke) || this.nextShotIsaHazard(next_stroke)) {
      // self.next_stroke.try(:next_stroke)
      return this.generateNextStroke(all_strokes, next_stroke);
    } else {
      return next_stroke;
    }
  }

  strokes_gained() {
    // get_strokes_gained
  }

  set_distance_from_pin() {
    // return if missing_coordinates? || missing_pin_location?
    // distance = self.coords.
    //     distance(self.hole_played.pin_location).
    //     round(2)
    // distance
  }
  async update_stroke_coordinate(strokeId, point) {
    try {
      let coords = point;
      if (isArray(coords)) {
        coords = {
          type: 'Point',
          coordinates: point,
        };
      }
      const result = await this.strokePlayedRepo.update({ id: strokeId }, { coords });
      this.logger.log(`UPDATE STROKE COORDS ${strokeId} - COORDS: ${point} - ${result}`);
    } catch (error) {
      this.logger.error(error.message);
    }
  }
  update_coordinates() {
    try {
      // nextstroke              = self.next_stroke
      // s_geometry              = self.geometry
      // self.coords             = factory.point(s_geometry[0].to_f, s_geometry[1].to_f)  if s_geometry.present?
      // self.coords             = coords_to_rgeo                                         if self.coords.is_a?(Array)
      // self.lie                = find_lie                                               if self.lie.blank?
      // self.subsequent_lie     = nextstroke.try(:find_lie)                              if nextstroke.present?
      // return unless self.coords.present?
      // if self.club_id.nil? && self.lie =~/green/i
      //   club_putter = user.clubs.active.putters.first
      //   if club_putter.present?
      //     self.club_id = club_putter.id
      //   end
      // end
      // self.distance_from_pin  = self.coords.distance(pin_location)
      // if !(missing_coordinates? || missing_pin_location?) &&
      //     nextstroke.present?                       &&
      //     !nextstroke.missing_coordinates?
      //   self.distance           = self.coords.distance(nextstroke.try(:coords))
      //   self.result_from_pin    = nextstroke.coords.distance(pin_location)
      // end
    } catch (error) {
      return null;
    }
  }

  //#
  //# Mark manually added if the coords changed and it
  //# was auto_moved or neither auto_moved and auto_detected.
  //#
  update_status() {
    // self.manually_moved = true if coords_changed?
  }

  // #
  // # Overloaded comparison for checking if coords changed.
  // # Converts to array before the comparison.
  // #
  coords_changed() {
    // return false unless coords_change
    // a = convert_to_array( coords_change.first )
    // b = convert_to_array( coords_change.last )
    // !(a == b)
  }

  update_metadata() {
    // self.round_id         ||= self.hole_played.try(:round_id)
    // update_coordinates
    // self.ordinal          ||= last_stroke.try(:ordinal).to_i + 1
  }

  adjust_tee_shots() {
    // return if self.lie =~ /tee/i   ||  missing_coordinates? #!is_first_stroke
    // # force TEE if its within the tee-boundary or the first stroke.
    // self.lie = 'Tee' if within_tee_boundary? #|| is_first_stroke
  }

  // #Returns two Booleans: the first indicates whether the shot landed left of the pin; the second, whether it landed long
  relative_quadrants_from_pin() {
    // rel = self.relative_result_angle_from_pin
    // return  (0..Math::PI).cover?(rel), ((Math::PI * 0.5)..(Math::PI * 1.5)).cover?(rel)
  }

  // #Returns the angle of where the shot wound up to the pin relative to the direction of the shot to the pin
  // #Used to work out if we're short/long and left/right
  relative_result_angle_from_pin() {
    // return if self.shot_azimuth.blank? || self.result_angle_from_pin.blank?
    // @relative_to_pin ||= (self.result_angle_from_pin - self.azimuth_to_pin) % (2 * Math::PI) rescue 0
  }
  async updateStroke(strokeId, payload) {
    try {
      await this.strokePlayedRepo.update({ id: strokeId }, payload);
    } catch (error) {
      this.logger.error('ERROR UPDATE STROKE PLAYED');
      this.logger.error(error.message);
    }
  }

  determine_y_distance(type, stroke) {
    const statsHelper = new StatsHelper();
    switch (type) {
      case 'short':
        return statsHelper.calculate_y_axis(stroke.green_distances);
      case 'approach':
        return statsHelper.calculate_y_axis(stroke.green_distances);
      default:
        return 0;
    }
  }

  determine_x_distance(type, stroke) {
    const statsHelper = new StatsHelper();
    switch (type) {
      case 'short':
        return statsHelper.calculate_x_axis(stroke.green_distances);
      case 'approach':
        return statsHelper.calculate_x_axis(stroke.green_distances);
      default:
        return 0;
    }
  }

  determine_x_coordinate_direction(stroke) {
    // return nil if fairway_centerline.blank?

    // case fairway_centerline.shot_direction
    // when "right"
    //   fairway_centerline.shot_distance_from_center
    // when "left"
    //   fairway_centerline.shot_distance_from_center * -1
    // when "center"
    //   0
    // end
    // return nil if fairway_centerline.blank?
    const fwCenter: any = this.fairway_centerline(stroke);
    if (!fwCenter) {
      return null;
    }

    switch (fwCenter?.shot_direction) {
      case 'right':
        return fwCenter?.shot_distance_from_center;
      case 'left':
        return fwCenter?.shot_distance_from_center * -1;
      case 'center':
        return 0;
    }
  }

  get_fairway_x_coordinate(stroke, location) {
    const fwDistance = stroke.fairway_distance_from_center;
    if (fwDistance == null) {
      return null;
    }
    const fwLocation = stroke.fairway_location_from_center;
    if (fwLocation == null) {
      return 0;
    }
    switch (location) {
      case 'right':
        return stroke.fairway_distance_from_center;
      case 'left':
        return stroke.fairway_distance_from_center * -1;
      default:
        return 0;
    }
  }

  driving_graph(stroke) {
    const statsHelper = new StatsHelper();
    const data = {
      x: this.get_fairway_x_coordinate(stroke, ''),
      y: (stroke.shot_distance * Stats.METERS_TO_YARDS).toFixed(2),
      color: statsHelper.lie_color(stroke.ending_lie),
      bulletBorderColor: statsHelper.lie_color(stroke.ending_lie),
      id: stroke.stroke_played_id,
      lie: stroke.starting_lie,
      end_lie: stroke.ending_lie,
      hole_name: stroke.hole_name,
      ordinal: stroke.stroke_ordinal,
      location: stroke.fairway_location_from_center,
    };
    return data;
  }
  async shot_to_center_bearing(stroke, fairway_center, holePlayed, fwPolygon) {
    const endpoint_closest_to_shot = await this.endpoint_closest_to_shot(stroke, fairway_center, holePlayed, fwPolygon);
    // fs.appendFileSync('shot_bearing.log', JSON.stringify({ endpoint_closest_to_shot }) + '\n');
    // fs.appendFileSync('shot_bearing.log', JSON.stringify({ fairway_center }) + '\n');
    // fs.appendFileSync('shot_bearing.log', JSON.stringify({ fwPolygon }) + '\n');
    return await this.get_bearing(
      endpoint_closest_to_shot,
      fairway_center || (await this.get_true_fairway_center(fairway_center, holePlayed, fwPolygon))
    );
  }

  async shot_bearing(stroke, nextstroke, fairway_center, holePlayed, fwPolygon) {
    const shot_to_center_bearing = await this.shot_to_center_bearing(stroke, fairway_center, holePlayed, fwPolygon);
    const shot_to_next_bearing = await this.shot_to_next_bearing(
      stroke,
      nextstroke,
      fairway_center,
      holePlayed,
      fwPolygon
    );
    return this.formatBearing(shot_to_center_bearing - shot_to_next_bearing);
  }
  async shot_to_next_bearing(stroke, nextstroke, fairway_center, holePlayed, fwPolygon) {
    if (!nextstroke) {
      return 0;
    }
    const strokeLanded = nextstroke?.coords?.coordinates || null;
    // fs.appendFileSync(
    //   'shot_bearing.log',
    //   JSON.stringify({
    //     strokeLanded,
    //   }) + '\n'
    // );
    const endpoint_closest_to_shot = await this.endpoint_closest_to_shot(stroke, fairway_center, holePlayed, fwPolygon);
    return this.get_bearing(endpoint_closest_to_shot, strokeLanded);
  }

  async shot_direction(stroke, nextstroke, holePlayed) {
    // fairway_polygon(holePlayed) {
    //   return this.generateFairwayPolygon(holePlayed);
    // }
    if (!nextstroke) {
      return null;
    }
    const fairway_polygon = await this.fairway_polygon(holePlayed);
    if (isEmpty(fairway_polygon)) {
      return null;
    }
    const fairway_center = getCenterPoint(fairway_polygon[fairway_polygon.length - 1]);
    // return nil if @fairway_polygon.blank?
    // if s_shot_bearing > 0 && s_shot_bearing < 180
    //   "left"
    // elsif s_shot_bearing >= 180 && s_shot_bearing < 360
    //   "right"
    // else
    //    "center"
    // end
    const s_shot_bearing = await this.shot_bearing(stroke, nextstroke, fairway_center, holePlayed, fairway_polygon);

    if (s_shot_bearing > 0 && s_shot_bearing < 180) {
      return 'left';
    } else if (s_shot_bearing >= 180 && s_shot_bearing < 360) {
      return 'right';
    } else {
      return 'center';
    }
  }

  async shot_distance_from_center(nextstroke, stroke, allStrokes, holePlayed) {
    // return nil if @fairway_polygon.blank?
    if (!nextstroke) {
      return null;
    }
    const fwPolygon = await this.fairway_polygon(holePlayed);
    if (isEmpty(fwPolygon)) {
      return null;
    }
    let intersect = await this.shot_intersect_from_center(nextstroke, stroke, allStrokes, holePlayed, [
      fwPolygon[fwPolygon.length - 1],
    ]);
    if (isEmpty(intersect) && fwPolygon.length > 1) {
      this.logger.log(`SHOT_DISTANCE_FROM_CENTER INTERSECT: ${intersect}`);
      intersect = await this.shot_intersect_from_center(
        nextstroke,
        stroke,
        allStrokes,
        holePlayed,
        fwPolygon.reverse()
      );
    }
    if (isEmpty(intersect)) {
      this.logger.log(`EMPTY INTERSECT: ${intersect}`);
      return 0;
    }
    const strokeLanded = nextstroke.coords.coordinates;
    // return 0 if intersect.blank?
    this.logger.debug(`STROKE ORDINAL: ${stroke.ordinal}`);
    return this.calculate_distance(intersect, strokeLanded);
  }
  calculate_distance(centerpoint, endpoint) {
    const dist_in_yards = getDistancePoints(centerpoint, endpoint);
    // const dist_in_yards = dist * Stats.MILES_TO_YARDS;
    return isNaN(dist_in_yards) ? 0 : dist_in_yards;
  }

  async shot_intersect_from_center(nextstroke, stroke, allStrokes, holePlayed, fwPolygon) {
    const east_intersect = await this.shot_intersect_point(
      'east',
      nextstroke,
      stroke,
      allStrokes,
      holePlayed,
      fwPolygon
    );
    if (!east_intersect || east_intersect.length == 0) {
      const west_intersect = await this.shot_intersect_point(
        'west',
        nextstroke,
        stroke,
        allStrokes,
        holePlayed,
        fwPolygon
      );
      return west_intersect;
    } else {
      return east_intersect;
    }
  }
  async shot_intersect_point(bearing, nextstroke, stroke, allStrokes, holePlayed, fwPolygon) {
    nextstroke = nextstroke ? nextstroke : this.generateNextStroke(allStrokes, stroke);
    if (isPointEmpty(nextstroke)) {
      return [];
    }

    const point: any = await this.get_intersection_of_centerline(
      bearing,
      nextstroke,
      stroke,
      allStrokes,
      holePlayed,
      fwPolygon
    );
    if (!point) {
      return [];
    }
    return point;
    // return nil if point.is_a?(::RGeo::Geos::FFIGeometryCollectionImpl)
    // if point.is_a?(::RGeo::Geos::FFIMultiPointImpl)
    //   [ point[1].y, point[1].x ]
    // else
    // [ point.y, point.x ]
    // end
  }

  async get_intersection_of_centerline(bearing, nextstroke, stroke, allStrokes, holePlayed, fwPolygon) {
    nextstroke = nextstroke ? nextstroke : this.generateNextStroke(allStrokes, stroke);
    if (isPointEmpty(nextstroke)) {
      return [];
    }

    const strokeLanded = nextstroke.coords.coordinates;

    const heading =
      bearing == 'west'
        ? await this.west_bearing(holePlayed, fwPolygon)
        : await this.east_bearing(holePlayed, fwPolygon);
    const endpoint = GeocoderCalculations.endpoint(strokeLanded, heading, 0.2);

    const line = this.create_line(strokeLanded, endpoint);
    // const fairway_polygon = fwPolygon; //await this.fairway_polygon(holePlayed);
    // if (isEmpty(fairway_polygon)) {
    //   return [];
    // }
    // const fairway_center = getCenterPoint(fairway_polygon[0]); //turf.centroid(await this.fairway_polygon(holePlayed));

    // const draw_centerline = await this.draw_centerline(stroke, fairway_center, holePlayed, fairway_polygon);

    // const centerline = turf.multiLineString(draw_centerline); //factory.multi_line_string(draw_centerline);
    try {
      const fwPolygons = await this.igolfService.coordinatesFor(
        +holePlayed.name,
        'fairway_center_path',
        holePlayed.igolf_course_id
      );
      const lineFW = turf.lineString(fwPolygons[fwPolygons.length - 1]);
      const center: any = turf.lineIntersect(lineFW, line);
      return center.features[0]?.geometry?.coordinates;
    } catch (error) {
      this.logger.error(`GET_INTERSECTION_OF_CENTERLINE`);
      this.logger.error(error);
      return [];
    }

    // centerline.intersection(line);
  }
  async west_bearing(holePlayed, fwPolygon) {
    const first_edge_point_to_center_bearing = await this.first_edge_point_to_center_bearing(holePlayed, fwPolygon);
    return this.formatBearing(first_edge_point_to_center_bearing - 90);
  }
  async east_bearing(holePlayed, fwPolygon) {
    // format_bearing( first_edge_point_to_center_bearing + 90 )
    const first_edge_point_to_center_bearing = await this.first_edge_point_to_center_bearing(holePlayed, fwPolygon);
    return this.formatBearing(first_edge_point_to_center_bearing + 90);
  }

  async first_edge_point_to_center_bearing(holePlayed, fwPolygon) {
    const greatDistance = await this.get_endpoint_with_greatest_distance(holePlayed, fwPolygon);
    if (greatDistance) {
      return greatDistance['intersect_to_center_bearing'];
    }

    return 0;
  }

  async get_endpoint_with_greatest_distance(holePlayed, fwPolygon) {
    // const fairway_polygon = await this.fairway_polygon(holePlayed);
    // if (isEmpty(fairway_polygon)) {
    //   return null;
    // }
    const fairway_center = getCenterPoint(fwPolygon[0]);

    const compass_point = await this.get_fairway_compass_points(fairway_center, fwPolygon);

    if (isEmpty(compass_point)) {
      return null;
    }

    return await this.get_max_distance(compass_point);
    // return nil if compass_points.blank?
    // get_max_distance( compass_points )
  }
  get_max_distance(points) {
    const sortPoint = _.sortBy(points, 'distance').reverse();
    return sortPoint[0];
  }
  fairway_polygon(holePlayed) {
    return this.generateFairwayPolygon(holePlayed);
  }
  async get_fairway_compass_points(fairway_center, fwPolygon) {
    // return [] if fairway_center.blank?
    // points = []
    // 0.upto(360) do |bearing|
    //   intersect = generate_intersect_point(bearing)
    //   next if intersect.blank?
    //   distance  = calculate_distance(intersect, fairway_center)
    //   points << {
    //     distance:                       distance,
    //     intersect_to_center_bearing:    endpoint_to_fairway_bearing(intersect),
    //     intersect:                      intersect,
    //     center:                         fairway_center,
    //     bearing:                        bearing,
    //   }
    // end
    // # {:distance=>0, :intersect_to_center_bearing=>NaN, :intersect=>[], :center=>[33.08767934037908, -117.24922034391007], :bearing=>15},
    // #points.delete_if { |hash| hash[:intersect].blank? }
    // points
    if (isEmpty(fairway_center)) {
      return [];
    }
    const points = [];
    for (let bearing = 0; bearing <= 360; bearing++) {
      const intersect = this.generate_intersect_point(bearing, fairway_center, fwPolygon);
      if (isEmpty(intersect)) {
        continue;
      }
      const distance = this.calculate_distance(intersect, fairway_center);
      const point = {
        distance: distance,
        intersect_to_center_bearing: await this.endpoint_to_fairway_bearing(intersect, fairway_center),
        intersect: intersect,
        center: fairway_center,
        bearing: bearing,
      };
      points.push(point);
    }
    return points;
  }

  endpoint_to_fairway_bearing(endpoint, fairway_center) {
    return this.get_bearing(endpoint, fairway_center);
  }
  get_bearing(startpoint, endpoint) {
    const bearing = GeocoderCalculations.bearingBetween(startpoint, endpoint);
    return this.formatBearing(bearing);
  }
  generate_intersect_point(bearing, fairway_center, fwPolygon) {
    const endpoint = GeocoderCalculations.endpoint(fairway_center, bearing, 0.2);
    if (isEmpty(endpoint) || isEmpty(fairway_center)) {
      return [];
    }
    // fairway_center = convertArrToLocation(fairway_center);
    // fairway_center = [fairway_center.lon, fairway_center.lat];
    const line = this.create_line(fairway_center, endpoint);
    const fairway_polygon = fwPolygon; //await this.fairway_polygon(holePlayed);
    if (isEmpty(fairway_polygon)) {
      return [];
    }

    return this.get_intersect_point(line, fairway_polygon[fairway_polygon.length - 1]);
  }
  get_intersect_point(line, polygon) {
    let tmPolygon = polygon;
    if (!tmPolygon) {
      return [];
    }
    try {
      if (isArray(polygon)) {
        tmPolygon = turf.lineString(polygon);
      }
      const rgeo_line = turf.lineIntersect(line, tmPolygon);
      const features = rgeo_line.features;
      if (features) {
        const feature = features.pop();
        return feature?.geometry?.coordinates || [];
      } else {
        return [];
      }
    } catch (error) {
      this.logger.error(`GET_INTERSECT_POINT`);
      this.logger.error(error.message);
      return [];
    }
  }

  create_line(startPoint, endPoint) {
    return turf.lineString([startPoint, endPoint]);
  }
  buildPoint(coord: any) {
    if (!coord || coord.length == 0) {
      return null;
    }
    const pointObject: Point = {
      type: 'Point',
      coordinates: [coord[1], coord[0]],
    };
    return pointObject;
  }
  async west_point(fairway_center, holePlayed, fwPolygon) {
    const west_bearing = await this.west_bearing(holePlayed, fwPolygon);
    return this.generate_intersect_point(west_bearing, fairway_center, fwPolygon);
  }
  async east_point(fairway_center, holePlayed, fwPolygon) {
    const east_bearing = await this.east_bearing(holePlayed, fwPolygon);
    return this.generate_intersect_point(east_bearing, fairway_center, fwPolygon);
  }
  async get_true_fairway_center(fairway_center, holePlayed, fwPolygon) {
    return GeocoderCalculations.geographic_center([
      await this.west_point(fairway_center, holePlayed, fwPolygon),
      await this.east_point(fairway_center, holePlayed, fwPolygon),
    ]);
  }
  async first_edge_point(holePlayed, fwPolygon) {
    const point = await this.get_endpoint_with_greatest_distance(holePlayed, fwPolygon);
    return point['intersect'];
  }
  async get_half_fairway(fairway_center, holePlayed, fwPolygon) {
    const compass_point = await this.get_fairway_compass_points(fairway_center, fwPolygon);
    const west_bearing = await this.west_bearing(holePlayed, fwPolygon);
    const east_bearing = await this.east_bearing(holePlayed, fwPolygon);
    // if west_bearing < 180
    //   compass_points.select do |data|
    //     data[:bearing] > west_bearing && data[:bearing] < east_bearing
    //   end
    // else
    //   compass_points.select do |data|
    //     data[:bearing] > west_bearing || data[:bearing] < east_bearing
    //   end
    // end
    const points = compass_point.filter((point) => {
      return +point['bearing'] > +west_bearing || +point['bearing'] < +east_bearing;
    });
    return points;
  }
  async get_half_fairway_points(fairway_center, holePlayed, fwPolygon) {
    const halfFairway: any = await this.get_half_fairway(fairway_center, holePlayed, fwPolygon);
    const result = halfFairway?.map((hash) => {
      return {
        distance: this.calculate_distance(hash['intersect'], fairway_center),
        intersect: hash['intersect'],
        bearing: hash['bearing'],
      };
    });
    return result;
  }

  async last_edge_point(fairway_center, holePlayed, fwPolygon) {
    const fwPoints = await this.get_half_fairway_points(fairway_center, holePlayed, fwPolygon);
    const distance = this.get_max_distance(fwPoints);
    if (distance) {
      return distance['intersect'];
    }
    return [0, 0];
  }
  stroke_started(stroke) {
    return stroke.coords?.coordinates || [0, 0];
  }
  async endpoint_closest_to_shot(stroke, fairway_center, holePlayed, fwPolygon) {
    const first_edge_point = await this.first_edge_point(holePlayed, fwPolygon);
    const stroke_started = this.stroke_started(stroke);
    const last_edge_point = await this.last_edge_point(fairway_center, holePlayed, fwPolygon);
    const first_distance = this.calculate_distance(stroke_started, first_edge_point);
    const last_distance = this.calculate_distance(stroke_started, last_edge_point);

    return first_distance < last_distance ? first_edge_point : last_edge_point;
  }
  tee_center(holePlayed) {
    return this.igolfService.coordinatesFor(holePlayed.name, 'tee', holePlayed.igolf_course_id);
  }
  async draw_centerline(stroke, fairway_center, holePlayed, fwPolygon) {
    const center = fairway_center
      ? fairway_center
      : await this.get_true_fairway_center(fairway_center, holePlayed, fwPolygon);
    const first_edge_point = await this.first_edge_point(holePlayed, fwPolygon);
    const centerLine = [];
    if (first_edge_point) {
      const first_line = this.create_line(first_edge_point, center);
      centerLine.push(first_line);
    }
    const last_edge_point = this.last_edge_point(fairway_center, holePlayed, fwPolygon);
    if (last_edge_point) {
      const last_line = this.create_line(center, last_edge_point);
      centerLine.push(last_line);
    }
    const endpoint_closest_to_shot = await this.endpoint_closest_to_shot(stroke, fairway_center, holePlayed, fwPolygon);
    if (endpoint_closest_to_shot) {
      const tee_line = this.create_line(endpoint_closest_to_shot, this.tee_center(holePlayed));
      centerLine.push(tee_line);
    }

    // [ first_line, last_line, tee_line ].compact
    return centerLine;
  }
}
