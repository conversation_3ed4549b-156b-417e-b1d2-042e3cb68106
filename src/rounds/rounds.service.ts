import { InjectQueue } from '@nestjs/bull';
import { Cache } from '@nestjs/cache-manager';
import {
  BadRequestException,
  CACHE_MANAGER,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import _, { isEmpty } from 'lodash';
import roundNumber from 'lodash/round';
import moment from 'moment';
import { IPaginationOptions, paginateRaw } from 'nestjs-typeorm-paginate';
import { Between, In, IsNull, Not, Repository } from 'typeorm';
import { HANDICAP_CONSTANT } from 'src/cdm/cdm.constant';
import { CdmService } from 'src/cdm/cdm.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { UpdateScoreGhinDto } from 'src/ghin/dto/update-score-ghin.dto';
import { GhinRound } from 'src/ghin/entities/ghin.round.entity';
import { GHIN_ERROR_CODES, GHIN_SCORE_TYPES, GHIN_SOURCE_TYPES } from 'src/ghin/ghin.const';
import { GhinService } from 'src/ghin/ghin.service';
import { APPROACH_SHOT_ACCURACY, DRIVE_ACCURACY, IGolfCourse } from 'src/ghin/ghin.types';
import { GolfNetScoreHoleDto } from 'src/golfnet/dto/golfnet-score.hole.dto';
import { GOLF_NET_ERROR_CODES } from 'src/golfnet/golfnet.consts';
import { GolfNetService } from 'src/golfnet/golfnet.service';
import { GolfNetCourseMapped } from 'src/golfnet/golfnet.types';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { MytmService } from 'src/mytm/mytm.service';
import { PlayersService } from 'src/players/players.service';
import { RoundAuditImportMobileService } from 'src/round-audit-import-mobile/round-audit-import-mobile.service';
import { RoundAuditUpdateService } from 'src/round-audit-update/round-audit-update.service';
import { RoundAudit } from 'src/round-audit/entities/round-audit.entity';
import { RoundAuditService } from 'src/round-audit/round-audit.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { CreatePartnerUserDto } from 'src/users/dto/create-user.dto';
import { UserGender } from 'src/users/dto/update-user.dto';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { formatDate } from 'src/utils/date-utils';
import { throwBadRequestError, throwNotFoundError } from 'src/utils/exception';
import { transformDataPaging } from 'src/utils/infinity-pagination';
import { deleteValueBlank, includeStr, isGear, isValidId, sleeps } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { GolfNetScoreDto } from '../golfnet/dto/golfnet-score.dto';
import { GolfNetRoundEntity } from '../golfnet/entities/golf-net-round.entity';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';
import { CreateRoundTagDto, HoleDto } from './dto/create-round-tag.dto';
import { CreateRoundDto } from './dto/create-round.dto';
import { GhinRoundDto } from './dto/ghin-round.dto';
import { GhinScoreDto } from './dto/ghin-score.dto';
import { GhinScoreHoleDto } from './dto/ghin-score.hole.dto';
import { GolfNetRoundDto } from './dto/golf-net-round.dto';
import { PostScoreGolfNetDto } from './dto/post-score-golfnet.dto';
import { PostScoreDto } from './dto/post-score.dto';
import { RoundIdsDto } from './dto/round-ids.dto';
import { UpdateRoundTagDto } from './dto/update-round-tag.dto';
import { Round } from './entities/round.entity';
import { OPTIONS_JOB_DEFAULT, OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND, ROUND } from './round.const';
import { RoundCronService } from './round.cron.service';

@Injectable()
export class RoundService {
  private readonly logger = new Logger(RoundService.name);
  constructor(
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectRepository(GhinRound)
    private ghinRoundRepo: Repository<GhinRound>,
    @InjectRepository(GolfNetRoundEntity)
    private golfNetRoundRepo: Repository<GolfNetRoundEntity>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(RoundAudit)
    private roundAuditRepo: Repository<RoundAudit>,
    @InjectRepository(StrokePlayed)
    private strokePlayerRepo: Repository<StrokePlayed>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(forwardRef(() => HolesPlayedService))
    private holesPlayedService: HolesPlayedService,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    @Inject(forwardRef(() => PlayersService))
    private playerService: PlayersService,
    private configService: ConfigService,
    @Inject(forwardRef(() => ClubsService))
    private clubsService: ClubsService,
    @Inject(forwardRef(() => StrokesStatsService))
    private strokesStatsService: StrokesStatsService,
    @Inject(forwardRef(() => StrokesPlayedService))
    private strokePlayedService: StrokesPlayedService,
    @Inject(forwardRef(() => RoundAuditService))
    private roundAuditService: RoundAuditService,
    @Inject(forwardRef(() => ThreePartyCourseService))
    private threePartyCourseService: ThreePartyCourseService,

    @Inject(forwardRef(() => MytmService))
    private myTMService: MytmService,

    @Inject(forwardRef(() => RoundCronService))
    private roundCronService: RoundCronService,

    @Inject(forwardRef(() => GhinService))
    private ghinService: GhinService,

    @Inject(forwardRef(() => GolfNetService))
    private golfNetService: GolfNetService,

    @Inject(forwardRef(() => RoundAuditImportMobileService))
    private roundAuditImportMobileService: RoundAuditImportMobileService,
    @Inject(forwardRef(() => RoundAuditUpdateService))
    private roundAuditUpdate: RoundAuditUpdateService,
    @InjectQueue(PROCESSORS.IGolfRoundCompleteJob) private iGolfRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.ForceRoundCompleteJob) private forceRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.IGolfRoundDrivingDispersionJob) private iGolfRoundDrivingDispersionJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalcHandicapJob) private calcHandicapJobQueue: Queue,
    @InjectQueue(PROCESSORS.SimpleScoreToParJob) private simpleScoreToParQueue: Queue,
    @InjectQueue(PROCESSORS.CalculateAverageScoreJob) private averageScoreQueue: Queue,
    @InjectQueue(PROCESSORS.RoundUpdateJob) private roundUpdateQueue: Queue
  ) {}

  /**
   * postRound
   *
   * @param userId
   * @param createRoundDto
   * @returns
   */
  async postRound(userId: number, createRoundDto: CreateRoundDto) {
    const cacheErrRoundMessage: any = await this.getCacheErrRoundMessage();
    if (cacheErrRoundMessage !== 'FAILED') {
      return throwBadRequestError(cacheErrRoundMessage);
    }
    if (!createRoundDto.round.user_id) {
      createRoundDto.round.user_id = userId;
    }
    if (isNaN(createRoundDto.round.user_id)) {
      return {};
    }
    return await this.roundAuditService.postRoundAudit(createRoundDto);
  }

  /**
   * createRoundTag
   *
   * @param createRoundTagDto
   * @param authToken
   * @returns
   */
  async createRoundTag(createRoundTagDto: CreateRoundTagDto, company: string) {
    try {
      const { round } = createRoundTagDto;

      // Create or get partner user
      const partnerUserDto: CreatePartnerUserDto = {
        user: {
          tag_user_id: round.user_id,
          tag_user_email: round.email,
        },
      };

      const user = await this.usersService.createPartnerUser(partnerUserDto, company);

      // Get hole data for the course
      let holes = [];
      let courseYards = 0;
      let coursePar = 0;
      const mapCourse = round.map_course || 'TAG';
      const holeData = this.getHoleData(round.holes);
      holes = holeData.holes;
      courseYards = holeData.course_yards;
      coursePar = holeData.course_par;

      // Format data for postRoundAudit
      const createRoundDto: CreateRoundDto = {
        round: {
          holes: holes,
          user_id: user.id,
          tee_name: round.tee_name,
          course_id: round.course_id,
          course_name: round.course_name,
          map_id: mapCourse,
          generated_by: round.generated_by,
          played_on_utc: new Date(round.played_on_utc),
          user_timezone: round.user_timezone,
          completed: false,
          inprogress: true,
          igolf_course_id: round.course_id,
          played_on: new Date(round.played_on_utc),
          round_type: 'Practice',
          play_service: 'TAG',
          play_client: 'TAG',
          total_score: 0,
          players: [],
          round_mode: ROUND.ROUND_MODE_ADVANCED,
          course_yards: courseYards,
          course_par: coursePar,
        } as any,
        round_submit: true,
      };
      // return createRoundDto;
      return await this.roundAuditService.postRoundAudit(createRoundDto, mapCourse);
    } catch (err) {
      return { error: err?.message || err };
    }
  }

  /**
   * updateRoundTag
   *
   * @param roundId - Round ID to update
   * @param updateRoundTagDto - Update data
   * @returns Updated round
   */
  async updateRoundTag(roundId: number, updateRoundTagDto: UpdateRoundTagDto) {
    try {
      if (!roundId) {
        throwNotFoundError('RoundId not found');
      }
      const { round } = updateRoundTagDto;
      const roundDB = await this.findOne({
        where: {
          id: roundId,
        },
      });
      if (!roundDB) {
        throwNotFoundError('Round not found');
      }

      if (isEmpty(round.holes) && round.completed) {
        const updateRoundCompleteDto: any = {
          round: {
            user_id: roundDB?.user_id,
            completed: true,
            inprogress: false,
          },
          round_submit: true,
        };
        const updateStatusComplete = await this.update(roundId, updateRoundCompleteDto);
        return {
          round_id: roundId,
          updated: !!updateStatusComplete || false,
        };
      }
      // Format holes data to match existing structure
      const formattedHoles = (round.holes || []).map((hole) => ({
        par: hole.par,
        score: hole.score,
        number: hole.number,
        yards: hole?.distance || null,
        strokes: (hole.strokes || []).map((stroke) => ({
          coords: stroke.coords,
          ordinal: stroke.ordinal,
          penalty: stroke.penalty,
          starting_lie: stroke.starting_lie,
        })),
        pin_location: hole.pin_location || [],
        stroke_index: hole.handicap?.toString(),
      }));

      // Format data for existing update method
      const updateRoundDto: CreateRoundDto = {
        round: {
          user_id: roundDB?.user_id, // Will be ignored in update method
          holes: formattedHoles,
          total_score: round.total_score,
          completed: round.completed,
          inprogress: !round.completed,
          round_mode: roundDB.round_mode || ROUND.ROUND_MODE_ADVANCED,
          played_on: roundDB.played_on,
          round_type: roundDB.round_type,
          map_id: roundDB.map_id,
          generated_by: roundDB.generated_by,
        } as any,
        round_submit: true,
      };
      const forceMapCourse = roundDB.map_id.toLowerCase();
      // return updateRoundDto;
      const updateRound = await this.update(roundId, updateRoundDto, forceMapCourse);
      return {
        round_id: roundId,
        updated: !!updateRound || false,
      };
    } catch (err) {
      return { error: err?.message || err };
    }
  }

  /**
   * getHoleDatas
   *
   * Format incoming holes from TAG payload into the system structure.
   * No external course service calls.
   *
   * @param holes - Array of holes from payload (number, par, distance, handicap, pin_location)
   * @returns Object containing formatted holes array, course_yards, and course_par
   */
  getHoleData(holes: HoleDto[]) {
    try {
      const safeHoles = Array.isArray(holes) ? holes : [];

      const formattedHoles = safeHoles.map((hole, index) => {
        const par = Number(hole?.par) || 0;
        const number = Number(hole?.number) || index + 1;
        const distance = Number((hole as any)?.distance) || 0;
        const handicap = Number((hole as any)?.handicap) || 0;

        // Support both pin_location and coords (if DTO changed)
        const pinLocation = (hole as any)?.pin_location || [];

        return {
          par,
          score: 0,
          number,
          strokes: [],
          pin_location: pinLocation,
          stroke_index: handicap,
          yards: distance,
        };
      });

      const course_yards = formattedHoles.reduce((sum, h) => sum + (Number(h.yards) || 0), 0);
      const course_par = formattedHoles.reduce((sum, h) => sum + (Number(h.par) || 0), 0);

      return {
        holes: formattedHoles,
        course_yards,
        course_par,
      };
    } catch (error) {
      this.logger.error(`Error formatting hole data: ${error?.message || error}`);
      throw error;
    }
  }

  /**
   * postRoundUSGA
   *
   * @param userId
   * @param createRoundDto
   * @returns
   */
  async postRoundUSGA(userId: number, createRoundDto: CreateRoundDto) {
    const cacheErrRoundMessage: any = await this.getCacheErrRoundMessage();
    if (cacheErrRoundMessage !== 'FAILED') {
      return throwBadRequestError(cacheErrRoundMessage);
    }
    if (!createRoundDto.round.user_id) {
      createRoundDto.round.user_id = userId;
    }
    if (isNaN(createRoundDto.round.user_id)) {
      return {};
    }
    return await this.roundAuditService.postRoundAuditUSGA(createRoundDto);
  }

  async getCacheErrRoundMessage() {
    const cacheKey = `_ERROR:ROUND_MESSAGE`;
    let service = await this.cacheManager.get(cacheKey);

    if (!service) {
      service = 'FAILED';
      await this.cacheManager.set(cacheKey, service);
    }

    return service;
  }

  /**
   * createRound
   *
   * @param roundData
   * @returns
   */
  async createRound(roundData: any) {
    return await this.roundRepo.save(this.roundRepo.create(roundData));
  }

  /**
   * findAll
   *
   * @param userId
   * @param pagingOptions
   * @param options
   * @returns
   */
  async findAll(userId: number, pagingOptions: IPaginationOptions, options: any) {
    if (options.usga && options.golfnet) {
      throwBadRequestError('Only one of usga or golfnet can be true');
    }
    if (options.usga) {
      return await this.findAllGhinRound(userId, pagingOptions, options);
    } else if (options.golfnet) {
      return this.findAllGolfNetRound(userId, pagingOptions, options);
    }
    const queryBuilderPaging = this.roundRepo.createQueryBuilder('round');
    queryBuilderPaging.select(['id']);
    if (options.completed && (options.completed == true || options.completed == 'true')) {
      queryBuilderPaging.where('user_id = :userId AND completed = :completed', {
        userId,
        completed: true,
      });
    } else {
      queryBuilderPaging.where('user_id = :userId', {
        userId,
      });
      delete options.completed;
    }
    const conditionUSGA = options.usga ? ` ghin_round_id IS NOT NULL AND ghin_round_id != '' ` : null;
    if (conditionUSGA) {
      queryBuilderPaging.andWhere(conditionUSGA);
    }
    delete options.usga;

    const conditionGolfNet = options.golfnet ? ` golfnet_round_id IS NOT NULL AND golfnet_round_id != '' ` : null;
    if (conditionGolfNet) {
      queryBuilderPaging.andWhere(conditionGolfNet);
    }
    delete options.golfnet;

    let roundsPaging = await paginateRaw(queryBuilderPaging, pagingOptions);
    roundsPaging = transformDataPaging(roundsPaging);
    const roundQueryBuilder = this.roundRepo.createQueryBuilder();
    roundQueryBuilder.where({ user_id: userId, ...options });
    if (conditionUSGA) {
      roundQueryBuilder.andWhere(conditionUSGA);
    }
    if (conditionGolfNet) {
      roundQueryBuilder.andWhere(conditionGolfNet);
    }
    roundQueryBuilder.select([
      'id',
      'tee_name',
      'course_id',
      'course_name',
      'generated_by',
      'played_on',
      'created_at',
      'duration',
      'course_par',
      'completed',
      'user_timezone',
      'inprogress',
      'round_mode',
      'round_type',
      'igolf_course_id',
      'simple_score_to_par',
      'number_of_holes_played',
      'multiplayer_game_type',
      'map_id',
      'play_service',
      'play_client',
      'stats_completed',
      'number_of_holes_played',
      'front_9_score',
      'back_9_score',
      'eighteen_holes_score',
      'ghin_round_id',
      'arccos_round_id',
      'golfnet_round_id',
      'source_type',
      'ghin_course_id',
      'ghin_course_name',
      'ghin_tee_set_id',
      'ghin_tee_set_name',
      'ghin_score',
    ]);
    roundQueryBuilder.take(+pagingOptions.limit);
    roundQueryBuilder.skip((+pagingOptions.page - 1) * +pagingOptions.limit);
    roundQueryBuilder.orderBy({ played_on: 'DESC' });
    const rounds = await roundQueryBuilder.getRawMany();

    if (rounds) {
      const roundIds = rounds.map((r) => r.id);
      const holesOfRounds = await this.holesPlayedService.findRaw({
        where: {
          round_id: In(roundIds),
        },
        select: [
          'id',
          'round_id',
          'name',
          'par',
          'yards',
          'handicap_index',
          'stroke_index',
          'created_at',
          'score',
          'shots_removed',
          'image_url',
          'svg_image_url',
          'ST_AsGeoJSON("pin_location")::json AS pin_location',
          'fw_stats',
          'gr_stats',
          'bunker_hit',
          'penalty_hit',
          'putts_number',
        ],
      });
      for (let round of rounds) {
        const roundHoles = [];
        roundHoles['holes'] = [];
        const holesRound = holesOfRounds.filter((hole) => hole.round_id == round.id);
        round.holes = holesRound.sort((a, b) => +a.name - +b.name);

        const roundScore = this.getScore(round);
        let scoreToPar = 0;
        let totalHoleScore = 0;

        let holeNumbersPlayers = 0;

        round['stats_completed'] = await this.isStatsCompleted(round, roundScore);

        if (round.holes) {
          const lstHoleScores: any = await this.holesPlayedService.getScoreListHole(
            round.round_mode,
            round.holes,
            round.id
          );
          const holes: any = round.holes;

          for (const hole of holes) {
            const holeData = {};

            const resultHoleScore = lstHoleScores.find((h: any) => +h.id == +hole.id);

            const hole_score = resultHoleScore?.score || 0;

            if (hole_score > 0) {
              totalHoleScore += +hole_score;
              holeNumbersPlayers++;
              scoreToPar += +hole_score - +hole.par;
            }
            holeData['id'] = hole.id;
            holeData['hole_number'] = +hole.name;
            holeData['par'] = hole.par;
            holeData['yards'] = hole.yards;
            holeData['handicap'] = hole.handicap_index;
            holeData['total_score'] = hole_score + '';
            if (round.round_mode == ROUND.ROUND_MODE_CLASSIC) {
              holeData['fw_stats'] = hole.fw_stats;
              holeData['gr_stats'] = hole.gr_stats;
              holeData['bunker_hit'] = hole.bunker_hit;
              holeData['penalty_hit'] = hole.penalty_hit;
              holeData['putts_number'] = hole.putts_number;
            }
            const prepareHole = deleteValueBlank(holeData, false);
            roundHoles['holes'].push(prepareHole);
          }
          round['holes'] = roundHoles['holes'];
        }
        if (round.round_mode != ROUND.ROUND_MODE_SIMPLE) {
          round['total_score'] = totalHoleScore + '';
          // if (
          //   round.completed &&
          //   !round.stats_completed &&
          //   round.round_mode == ROUND.ROUND_MODE_ADVANCED &&
          //   totalHoleScore == 0
          // ) {
          //   round['total_score'] = roundScore + '';
          // }
        } else {
          round['total_score'] = roundScore + '';
        }

        if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
          round['number_of_holes_played'] = round.number_of_holes_played;
          round['score_to_par'] = round.simple_score_to_par;
          this.logger.warn(`ROUND SIMPLE: ${round.id} - ${round.simple_score_to_par}`);
          if (round.simple_score_to_par == null) {
            try {
              await this.simpleScoreToParQueue.add(
                PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR,
                {
                  roundId: round.id,
                },
                OPTIONS_JOB_DEFAULT
              );
            } catch (error) {
              console.log(error);
            }
          }
        } else {
          round['number_of_holes_played'] = holeNumbersPlayers;
          round['score_to_par'] = scoreToPar;
        }
        round['played_on'] = this.playedOnDate(round);
        round['created_at'] = formatDate(round['created_at']);
        // delete round['created_at'];
        // delete round['number_of_holes_played'];
        delete round['simple_score_to_par'];
        round = deleteValueBlank(round, false);
      }
    }

    roundsPaging['data'] = rounds;
    if (!rounds || rounds.length == 0) {
      return { data: [] };
    }
    return roundsPaging;
  }

  async findRoundTMAndUSGA(userId: number, pagingOptions: IPaginationOptions, options: any) {
    const queryBuilderPaging = this.roundRepo.createQueryBuilder('round');
    queryBuilderPaging.select(['id']);
    if (options.completed && (options.completed == true || options.completed == 'true')) {
      queryBuilderPaging.where('user_id = :userId AND completed = :completed', {
        userId,
        completed: true,
      });
    } else {
      queryBuilderPaging.where('user_id = :userId', {
        userId,
      });
      delete options.completed;
    }

    let roundsPaging = await paginateRaw(queryBuilderPaging, pagingOptions);
    roundsPaging = transformDataPaging(roundsPaging);
    const roundQueryBuilder = this.roundRepo.createQueryBuilder();
    roundQueryBuilder.where({ user_id: userId, ...options });
    roundQueryBuilder.select([
      'id',
      'tee_name',
      'course_id',
      'course_name',
      'generated_by',
      'played_on',
      'created_at',
      'duration',
      'course_par',
      'completed',
      'user_timezone',
      'inprogress',
      'round_mode',
      'round_type',
      'igolf_course_id',
      'simple_score_to_par',
      'number_of_holes_played',
      'multiplayer_game_type',
      'map_id',
      'play_service',
      'play_client',
      'stats_completed',
      'number_of_holes_played',
      'front_9_score',
      'back_9_score',
      'eighteen_holes_score',
      'ghin_round_id',
      'arccos_round_id',
      'golfnet_round_id',
      'source_type',
      'ghin_course_id',
      'ghin_course_name',
      'ghin_tee_set_id',
      'ghin_tee_set_name',
      'ghin_score',
    ]);
    roundQueryBuilder.take(+pagingOptions.limit);
    roundQueryBuilder.skip((+pagingOptions.page - 1) * +pagingOptions.limit);
    roundQueryBuilder.orderBy({ played_on: 'DESC' });
    const rounds = await roundQueryBuilder.getRawMany();

    if (rounds) {
      const roundIds = rounds.map((r) => r.id);
      const holesOfRounds = await this.holesPlayedService.findRaw({
        where: {
          round_id: In(roundIds),
        },
        select: [
          'id',
          'round_id',
          'name',
          'par',
          'yards',
          'handicap_index',
          'stroke_index',
          'created_at',
          'score',
          'shots_removed',
          'image_url',
          'svg_image_url',
          'ST_AsGeoJSON("pin_location")::json AS pin_location',
          'fw_stats',
          'gr_stats',
          'bunker_hit',
          'penalty_hit',
          'putts_number',
        ],
      });
      for (let round of rounds) {
        const roundHoles = [];
        roundHoles['holes'] = [];
        const holesRound = holesOfRounds.filter((hole) => hole.round_id == round.id);
        round.holes = holesRound.sort((a, b) => +a.name - +b.name);

        const roundScore = this.getScore(round);
        let scoreToPar = 0;
        let totalHoleScore = 0;

        let holeNumbersPlayers = 0;

        round['stats_completed'] = await this.isStatsCompleted(round, roundScore);

        if (round.holes) {
          const lstHoleScores: any = await this.holesPlayedService.getScoreListHole(
            round.round_mode,
            round.holes,
            round.id
          );
          const holes: any = round.holes;

          for (const hole of holes) {
            const holeData = {};

            const resultHoleScore = lstHoleScores.find((h: any) => +h.id == +hole.id);

            const hole_score = resultHoleScore?.score || 0;

            if (hole_score > 0) {
              totalHoleScore += +hole_score;
              holeNumbersPlayers++;
              scoreToPar += +hole_score - +hole.par;
            }
            holeData['id'] = hole.id;
            holeData['hole_number'] = +hole.name;
            holeData['par'] = hole.par;
            holeData['yards'] = hole.yards;
            holeData['handicap'] = hole.handicap_index;
            holeData['total_score'] = hole_score + '';
            if (round.round_mode == ROUND.ROUND_MODE_CLASSIC) {
              holeData['fw_stats'] = hole.fw_stats;
              holeData['gr_stats'] = hole.gr_stats;
              holeData['bunker_hit'] = hole.bunker_hit;
              holeData['penalty_hit'] = hole.penalty_hit;
              holeData['putts_number'] = hole.putts_number;
            }
            const prepareHole = deleteValueBlank(holeData, false);
            roundHoles['holes'].push(prepareHole);
          }
          round['holes'] = roundHoles['holes'];
        }
        if (round.round_mode != ROUND.ROUND_MODE_SIMPLE) {
          round['total_score'] = totalHoleScore + '';
          // if (
          //   round.completed &&
          //   !round.stats_completed &&
          //   round.round_mode == ROUND.ROUND_MODE_ADVANCED &&
          //   totalHoleScore == 0
          // ) {
          //   round['total_score'] = roundScore + '';
          // }
        } else {
          round['total_score'] = roundScore + '';
        }

        if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
          round['number_of_holes_played'] = round.number_of_holes_played;
          round['score_to_par'] = round.simple_score_to_par;
          this.logger.warn(`ROUND SIMPLE: ${round.id} - ${round.simple_score_to_par}`);
          if (round.simple_score_to_par == null) {
            try {
              await this.simpleScoreToParQueue.add(
                PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR,
                {
                  roundId: round.id,
                },
                OPTIONS_JOB_DEFAULT
              );
            } catch (error) {
              console.log(error);
            }
          }
        } else {
          round['number_of_holes_played'] = holeNumbersPlayers;
          round['score_to_par'] = scoreToPar;
        }
        round['played_on'] = this.playedOnDate(round);
        round['created_at'] = formatDate(round['created_at']);
        // delete round['created_at'];
        // delete round['number_of_holes_played'];
        delete round['simple_score_to_par'];
        round = deleteValueBlank(round, false);
      }
    }

    roundsPaging['data'] = rounds;
    if (!rounds || rounds.length == 0) {
      return { data: [] };
    }
    return roundsPaging;
  }

  async findAllGhinRound(userId: number, pagingOptions: IPaginationOptions, options: any) {
    const queryBuilderPaging = this.ghinRoundRepo.createQueryBuilder('round');
    queryBuilderPaging.select(['id']);
    queryBuilderPaging.where('user_id = :userId', {
      userId,
    });
    delete options.completed;
    delete options.usga;
    delete options.golfnet;
    let roundsPaging = await paginateRaw(queryBuilderPaging, pagingOptions);
    roundsPaging = transformDataPaging(roundsPaging);
    const roundQueryBuilder = this.ghinRoundRepo.createQueryBuilder();
    roundQueryBuilder.where({ user_id: userId, ...options });
    roundQueryBuilder.select([
      'id',
      'created_at',
      'played_on',
      'number_of_holes_played',
      'ghin_round_id',
      'score_type AS source_type',
      'ghin_course_id',
      'ghin_course_name',
      'ghin_tee_set_id',
      'ghin_tee_set_name',
      'score_to_par',
      'course_yards',
      'course_par',
      'generated_by',
      'score AS ghin_score',
    ]);
    roundQueryBuilder.take(+pagingOptions.limit);
    roundQueryBuilder.skip((+pagingOptions.page - 1) * +pagingOptions.limit);
    roundQueryBuilder.orderBy({ played_on: 'DESC', created_at: 'DESC' });
    const rounds = await roundQueryBuilder.getRawMany();

    if (rounds) {
      for (let round of rounds) {
        const roundHoles = [];
        roundHoles['holes'] = [];
        round['round_mode'] = ROUND.ROUND_MODE_SIMPLE;
        round['played_on'] = this.playedOnDate(round);
        round['created_at'] = formatDate(round['created_at']);
        // delete round['created_at'];
        round = deleteValueBlank(round, false);
      }
    }

    roundsPaging['data'] = rounds;
    if (!rounds || rounds.length == 0) {
      return { data: [] };
    }
    return roundsPaging;
  }

  async findAllGolfNetRound(userId: number, pagingOptions: IPaginationOptions, options: any) {
    const queryBuilderPaging = this.golfNetRoundRepo.createQueryBuilder('round');
    queryBuilderPaging.select(['id']);
    queryBuilderPaging.where('user_id = :userId', {
      userId,
    });
    delete options.completed;
    delete options.usga;
    delete options.golfnet;
    let roundsPaging = await paginateRaw(queryBuilderPaging, pagingOptions);
    roundsPaging = transformDataPaging(roundsPaging);
    const roundQueryBuilder = this.golfNetRoundRepo.createQueryBuilder();
    roundQueryBuilder.where({ user_id: userId, ...options });
    roundQueryBuilder.select([
      'id',
      'created_at',
      'played_on',
      'played_on_utc',
      'user_timezone',
      'number_of_holes_played',
      'golfnet_round_id',
      'score_type AS source_type',
      'golfnet_course_id',
      'golfnet_course_name',
      'golfnet_tee_set_id',
      'golfnet_tee_set_name',
      'score_to_par',
      'course_yards',
      'course_par',
      'generated_by',
      'score AS golf_net_score',
    ]);
    roundQueryBuilder.take(+pagingOptions.limit);
    roundQueryBuilder.skip((+pagingOptions.page - 1) * +pagingOptions.limit);
    roundQueryBuilder.orderBy({ played_on_utc: 'DESC', created_at: 'DESC' });
    const rounds = await roundQueryBuilder.getRawMany();

    if (rounds) {
      for (let round of rounds) {
        const roundHoles = [];
        roundHoles['holes'] = [];
        round['round_mode'] = ROUND.ROUND_MODE_SIMPLE;
        round['played_on'] = this.playedOnDate(round);
        round['created_at'] = formatDate(round['created_at']);
        round = deleteValueBlank(round, false);
      }
    }

    roundsPaging['data'] = rounds;
    if (!rounds || rounds.length == 0) {
      return { data: [] };
    }
    return roundsPaging;
  }

  async findOne(options: any) {
    return await this.roundRepo.findOne(options);
  }

  /**
   * forceUpdateRoundStats
   *
   * @param roundIds
   */
  forceUpdateRoundStats(roundIds: RoundIdsDto) {
    this.roundCronService.regenerateRoundMissingStats(roundIds);
  }

  async checkStatsCompleted(id: number) {
    const roundQueryBuilder = this.roundRepo.createQueryBuilder();
    roundQueryBuilder.where({ id });
    roundQueryBuilder.select(['id', 'stats_completed', 'round_mode', 'driving_stat_complete']);

    const round = await roundQueryBuilder.getRawOne();
    if (round) {
      if (round.round_mode != ROUND.ROUND_MODE_ADVANCED) {
        return { completed: true, dispersion_complete: true };
      }
      return { completed: round.stats_completed == true, dispersion_complete: round.driving_stat_complete == true };
    } else {
      return { completed: false, dispersion_complete: false };
    }
  }
  async findOneBy(id: number) {
    const roundQueryBuilder = this.roundRepo.createQueryBuilder();
    roundQueryBuilder.where({ id });

    const round = await roundQueryBuilder.getOne();

    let holes = [];
    if (round) {
      holes = await this.holesPlayedService.findRaw({
        where: {
          round_id: round.id,
        },
        select: [
          'id',
          'round_id',
          'name',
          'par',
          'yards',
          'handicap_index',
          'stroke_index',
          'created_at',
          'shots_removed',
          'image_url',
          'svg_image_url',
          'ST_AsGeoJSON("pin_location")::json AS pin_location',
          'fw_stats',
          'gr_stats',
          'bunker_hit',
          'penalty_hit',
          'putts_number',
          'score',
          'steps_count',
          'lowest_heartrate',
          'average_heartrate',
          'peak_heartrate',
          'calories_burned',
        ],
        // relations: ['strokes'],
      });

      const response = { ...round, holes };
      return await this.transformResponseRound(response);
    }
    return {};
  }

  /**
   * postScoreToGHIN
   *
   * @param id
   * @returns
   */
  async postScoreToGHIN(id: number) {
    const round: any = await this.findOneBy(id);
    if (round && !isEmpty(round)) {
      if (!round.ghin_course_id || !round.ghin_tee_set_id) {
        throw new BadRequestException({
          message: 'Round missing ghin_course_id or ghin_tee_rating_id',
          errorCode: GHIN_ERROR_CODES.ERROR_INVALID_DATA,
        });
      }
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.ghin_id) {
          throw new UnauthorizedException(`User don't have a GHIN id`);
        }
        if (!user.ghin_gpa_status) {
          throw new UnauthorizedException(`User need to acknowledge request golfer product access!`);
        }
        if (round.ghin_round_id) {
          throw new BadRequestException('The round has posted score to GHIN!');
        }

        // let userGender = user.gender;
        // if (!userGender || userGender.length > 6) {
        //   userGender = 'Male';
        // }

        this.validateScore(round);
        const ghinCourseDetail = await this.ghinService.getGHINCourseDetail(round.ghin_course_id);
        if (!ghinCourseDetail) {
          this.ghinService.throwGHINCourseNotFound();
        }
        const ghinTeeRating = ghinCourseDetail?.TeeSets?.find((tee) => tee.TeeSetRatingId == round.ghin_tee_set_id);
        if (!ghinTeeRating) {
          throw new BadRequestException({
            message: 'Not found tee set rating for course!',
            errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
            ghinCourseId: round.ghin_course_id,
          });
        }
        // Force get gender from tee set
        const userGender = ghinTeeRating?.Gender?.toLowerCase() == 'female' ? 'female' : 'male';

        const ghinCourse = {
          igolf_ghin: { ghin_course_id: ghinCourseDetail.CourseId },
          tee_set_rating: {
            holes_number: ghinTeeRating.HolesNumber,
            ghin_tee_rating_id: ghinTeeRating.TeeSetRatingId,
          },
        };

        let ghinScoreWithHoleDetails = null;
        if (
          [
            ROUND.ROUND_MODE_ADVANCED,
            ROUND.ROUND_MODE_CLASSIC,
            ROUND.ROUND_MODE_MULTIPLAYER,
            ROUND.ROUND_MODE_BASIC,
          ].includes(round.round_mode)
        ) {
          ghinScoreWithHoleDetails = this.transformGhinScoreHoleDetail(user, ghinCourse, round, userGender);
        }
        const ghinScoreDto: GhinScoreDto = this.transformGhinScore(user, ghinCourse, round, userGender);
        const isPostHoleByHole = ghinScoreWithHoleDetails != null;
        const score = await this.ghinService.postScoreToGHIN(ghinScoreDto, ghinScoreWithHoleDetails, isPostHoleByHole);
        await this.roundRepo.update(
          { id: round.id },
          {
            ghin_round_id: score.id,
          }
        );
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.response?.errorCode,
          ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
        });
      }
    }
    return {};
  }

  /**
   * postScoreToGHINHaveRound
   *
   * @param id
   * @returns
   */
  async forcePostScoreToGHINHaveRoundTM(round: any, isNewRound = false) {
    try {
      const roundTM: any = await this.findOneBy(round.id);
      const user = await this.userRepo.findOne({
        where: { id: round.user_id },
        select: ['gender', 'id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
      });

      roundTM.ghin_course_id = round.ghin_course_id;
      roundTM.ghin_tee_set_id = round.ghin_tee_set_id;
      roundTM.source_type = round.source_type;
      if (!isNewRound) {
        roundTM.played_on = round.played_on;
      }

      this.validateScore(roundTM);
      // round.total_score = this.getTotalScoreByRoundMode(round, roundTM?.total_score);
      const ghinCourseDetail = await this.ghinService.getGHINCourseDetail(roundTM.ghin_course_id);
      if (!ghinCourseDetail) {
        throw new BadRequestException({
          message: 'GHIN course not found!',
          errorCode: GHIN_ERROR_CODES.ERROR_MAP_GHIN_COURSE,
          ghinCourseId: null,
        });
      }
      const ghinTeeSetDetail = ghinCourseDetail.TeeSets.find((tee) => tee.TeeSetRatingId == round.ghin_tee_set_id);
      if (!ghinTeeSetDetail) {
        throw new BadRequestException({
          message: 'Not found tee set rating for course!',
          errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
          ghinCourseId: round.ghin_course_id,
        });
      }

      const userGender = ghinTeeSetDetail?.Gender || 'Male';

      const ghinCourse = {
        igolf_ghin: { ghin_course_id: ghinCourseDetail.CourseId },
        tee_set_rating: {
          holes_number: ghinTeeSetDetail.HolesNumber,
          ghin_tee_rating_id: ghinTeeSetDetail.TeeSetRatingId,
        },
      };
      let ghinScoreWithHoleDetails = null;
      if (
        [
          ROUND.ROUND_MODE_ADVANCED,
          ROUND.ROUND_MODE_CLASSIC,
          ROUND.ROUND_MODE_MULTIPLAYER,
          ROUND.ROUND_MODE_BASIC,
        ].includes(round.round_mode)
      ) {
        ghinScoreWithHoleDetails = this.transformGhinScoreHoleDetail(user, ghinCourse, roundTM, userGender);
      }

      const ghinScoreDto: GhinScoreDto = this.transformGhinScore(user, ghinCourse, roundTM, userGender);
      const isPostHoleByHole = ghinScoreWithHoleDetails != null;
      const score = await this.ghinService.postScoreToGHIN(
        ghinScoreDto,
        ghinScoreWithHoleDetails,
        isPostHoleByHole,
        true,
        round.id
      );
      if (score) {
        score['generated_by'] = round.generated_by;
        score['played_at'] = round.played_on;
        await this.createGhinRound(score, user, { ...ghinTeeSetDetail });
      }
      if (score != null && round.id) {
        if (roundTM) {
          const updateRoundTM = {
            ghin_score: score.adjusted_gross_score,
            ghin_round_id: score.id,
            ghin_course_id: score.course_id,
            ghin_course_name: score.course_display_value,
            ghin_tee_set_id: score.tee_set_id,
            ghin_tee_set_name: score.tee_name,
            source_type: round.source_type,
            tee_name: round.ghin_tee_set_name,
            inprogress: false,
            completed: true,
            played_on: roundTM.played_on,
            course_yards: ghinTeeSetDetail?.TotalYardage || 0,
          };
          await this.roundRepo.update({ id: roundTM.id }, updateRoundTM);
        }
      }
      return { success: true, score };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException({
        message: error.message,
        errorCode: error?.response?.errorCode,
        ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
      });
    }
  }

  /**
   * forcePostScoreToGHIN
   *
   * @param id
   * @returns
   */
  async forcePostScoreToGHIN(round: any) {
    let roundCheck = null;
    const roundId = round.id || null;
    if (round && round.id) {
      roundCheck = await this.roundRepo.findOne({ where: { id: round.id }, select: ['ghin_round_id', 'id'] });
      if (roundCheck.ghin_round_id) {
        throw new BadRequestException({
          errorCode: GHIN_ERROR_CODES.ERROR_INVALID_DATA,
          message: 'The round has posted score to GHIN!',
        });
      }
    }
    if (round && !isEmpty(round) && round.ghin_course_id && round.ghin_tee_set_id) {
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.ghin_id) {
          throw new UnauthorizedException(`User don't have a GHIN id`);
        }
        if (!user.ghin_gpa_status) {
          throw new UnauthorizedException(`User need to acknowledge request golfer product access!`);
        }
        let userGender = user.gender;
        if (!userGender || userGender.length > 6) {
          userGender = 'Male';
        }
        this.validateScore(round);
        round.total_score = this.getTotalScoreByRoundMode(round, 0);
        const ghinCourseDetail = await this.ghinService.getGHINCourseDetail(round.ghin_course_id);
        if (!ghinCourseDetail) {
          throw new BadRequestException({
            message: 'GHIN course not found!',
            errorCode: GHIN_ERROR_CODES.ERROR_MAP_GHIN_COURSE,
            ghinCourseId: null,
          });
        }
        const ghinTeeSetDetail = ghinCourseDetail.TeeSets.find((tee) => tee.TeeSetRatingId == round.ghin_tee_set_id);
        if (!ghinTeeSetDetail) {
          throw new BadRequestException({
            message: 'Not found tee set rating for course!',
            errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
            ghinCourseId: round.ghin_course_id,
          });
        }
        const ghinCourse = {
          igolf_ghin: { ghin_course_id: ghinCourseDetail.CourseId },
          tee_set_rating: {
            holes_number: ghinTeeSetDetail.HolesNumber,
            ghin_tee_rating_id: ghinTeeSetDetail.TeeSetRatingId,
          },
        };
        const ghinScoreDto: GhinScoreDto = this.transformGhinScore(user, ghinCourse, round, userGender);
        const isPostHoleByHole = false;
        const score = await this.ghinService.postScoreToGHIN(ghinScoreDto, null, isPostHoleByHole, roundId);
        if (score) {
          score['generated_by'] = round.generated_by;
          score['played_at'] = round.played_on;
          await this.createGhinRound(score, user, { ...ghinTeeSetDetail });
        }
        if (score != null && round.id) {
          if (roundCheck) {
            await this.roundRepo.update(
              { id: roundCheck.id },
              {
                ghin_score: score.adjusted_gross_score,
                ghin_round_id: score.id,
                ghin_course_id: score.course_id,
                ghin_course_name: score.course_display_value,
                ghin_tee_set_id: score.tee_set_id,
                ghin_tee_set_name: score.tee_name,
              }
            );
          }
        }
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.response?.errorCode,
          ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
        });
      }
    }
    return {};
  }

  /**
   * forcePostScoreToGHIN
   *
   * @param id
   * @returns
   */
  async forcePostScoreToGolfNet(round: any) {
    let roundCheck = null;
    if (round && round.id) {
      roundCheck = await this.roundRepo.findOne({ where: { id: round.id }, select: ['golfnet_round_id', 'id'] });
      if (roundCheck.golfnet_round_id) {
        throw new BadRequestException({
          errorCode: GOLF_NET_ERROR_CODES.ERROR_INVALID_DATA,
          message: 'The round has posted score to Golf Net!',
        });
      }
    }
    if (round && round.golf_net_course_id && round.golf_net_tee_id) {
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'canada_card_id'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.canada_card_id) {
          throw new UnauthorizedException(`User don't have a Golf Net id`);
        }
        let userGender = user.gender;
        if (!userGender || userGender.length > 6) {
          userGender = 'male';
        }
        this.validateScore(round);
        round.total_score = this.getTotalScoreByRoundMode(round, 0);
        const golfNetCourseDetail = await this.golfNetService.getCourseDetail(round.golf_net_course_id);
        if (!golfNetCourseDetail) {
          throw new BadRequestException({
            message: 'Golf Net course not found!',
            errorCode: GOLF_NET_ERROR_CODES.ERROR_COURSE_NOT_FOUND,
            golfNetCourseId: round?.golf_net_course_id,
          });
        }
        const golfNetTeeSetDetail = golfNetCourseDetail?.tees.find((tee) => tee.id == round.golf_net_tee_id);
        if (!golfNetTeeSetDetail) {
          throw new BadRequestException({
            message: 'Not found tee set rating for course!',
            errorCode: GOLF_NET_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
            golfNetCourseId: round?.golf_net_course_id,
          });
        }
        golfNetTeeSetDetail['golfnet_course_id'] = round.golf_net_course_id;
        golfNetTeeSetDetail['tee_set_rating_name'] = golfNetTeeSetDetail?.name;
        golfNetTeeSetDetail['golfnet_tee_rating_id'] = golfNetTeeSetDetail?.id;
        golfNetTeeSetDetail['gender'] = golfNetTeeSetDetail?.type;
        golfNetTeeSetDetail['holes_number'] = golfNetTeeSetDetail?.holes?.length;
        golfNetTeeSetDetail['tee_rating_detail'] = JSON.stringify(golfNetTeeSetDetail);
        const golfNetCourse: any = {
          igolf_golfnet: {
            golfnet_course_id: golfNetCourseDetail?.id,
            tee_set_id: golfNetTeeSetDetail?.id,
            tee_name: golfNetTeeSetDetail?.name,
            golfnet_course_name: golfNetCourseDetail?.name,
          },
          tee_set_rating: golfNetTeeSetDetail,
        };
        const golfNetScoreDto: GolfNetScoreDto = this.transformGolfNetScore(user, golfNetCourse, round, userGender);
        const score = await this.golfNetService.postScore(golfNetScoreDto, null);
        if (score) {
          await this.createGolfNetRound(score, user, golfNetTeeSetDetail, round);
        }
        if (score != null && round.id) {
          if (roundCheck) {
            await this.roundRepo.update(
              { id: roundCheck.id },
              {
                golfnet_round_id: score.id,
                golfnet_course_id: score.courseId,
                golfnet_course_name: score.courseName,
                golfnet_tee_set_id: score.teeId,
                golfnet_tee_set_name: score.teeName,
              }
            );
          }
        }
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.response?.errorCode,
          golfNetCourseId: round?.golf_net_course_id,
        });
      }
    }
    return {};
  }

  /**
   * postScoreToCanada
   *
   * @param id
   * @returns
   */
  postScoreToGolfNet(id: number) {
    return { id };
  }

  async postScoreToGolfNetBackUp(id: number) {
    const round: any = await this.findOneBy(id);
    if (round && !isEmpty(round)) {
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'canada_card_id'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.canada_card_id) {
          throw new UnauthorizedException(`User don't have a Canada card id`);
        }
        if (round.golfnet_round_id) {
          throw new BadRequestException('The round has posted score to GolfNet!');
        }
        let userGender = user.gender;
        if (!userGender || userGender.length > 6) {
          userGender = 'Male';
        }
        this.validateScore(round);
        const iGolfCourseMap: IGolfCourse = {
          tee: round.tee_name,
          course_par: round.course_par,
          course_name: round.course_name?.trim(),
          igolf_course_id: round.igolf_course_id,
          gender: userGender,
        };

        const golfNetCourse: GolfNetCourseMapped = await this.golfNetService.mappingIGolfGolfNetCourse(iGolfCourseMap);
        const golfNetScoreDto: GolfNetScoreDto = this.transformGolfNetScore(user, golfNetCourse, round, userGender);

        const score = await this.golfNetService.postScore(golfNetScoreDto);
        await this.roundRepo.update(
          { id: round.id },
          {
            golfnet_round_id: score.id,
            golfnet_course_id: score.courseId,
            golfnet_course_name: score.courseName,
            golfnet_tee_set_id: score.teeId,
            golfnet_tee_set_name: score.teeName,
          }
        );
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.response?.errorCode,
          golfNetCourseId: error?.response?.golfNetCourseId,
        });
      }
    }
    return {};
  }

  /**
   * confirmPostScoreToGolfNet
   *
   * @param id
   * @param postScoreDto
   * @returns
   */
  confirmPostScoreToGolfNet(id: number, postScoreDto: PostScoreGolfNetDto) {
    return { id, postScoreDto };
  }

  async confirmPostScoreToGolfNetBackUp(id: number, postScoreDto: PostScoreGolfNetDto) {
    const round: any = await this.findOneBy(id);
    if (round && !isEmpty(round)) {
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'canada_card_id'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.canada_card_id) {
          throw new UnauthorizedException(`User don't have a Canada card id`);
        }
        if (round.golfnet_round_id) {
          throw new BadRequestException('The round has posted score to GolfNet!');
        }
        let userGender = user.gender;
        if (!userGender || userGender.length > 6) {
          userGender = 'Male';
        }

        this.validateScore(round);

        const golfNetCourseDetail = await this.golfNetService.getCourseDetail(postScoreDto.golfnet_course_id);

        const golfNetCourseTees = golfNetCourseDetail.tees;
        let teeRatting = null;
        if (golfNetCourseTees.length > 0) {
          teeRatting = golfNetCourseTees.find((tee) => {
            return tee.id == postScoreDto.tee_set_id;
          });
        }
        teeRatting['golfnet_course_id'] = postScoreDto.golfnet_course_id;
        teeRatting['tee_set_rating_name'] = teeRatting.name;
        teeRatting['golfnet_tee_rating_id'] = teeRatting.id;
        teeRatting['gender'] = teeRatting.type;
        teeRatting['holes_number'] = teeRatting.holes?.length;
        teeRatting['total_yard_age'] = teeRatting?.yardage;
        teeRatting['total_par'] = teeRatting?.par;
        teeRatting['tee_rating_detail'] = JSON.stringify(teeRatting);

        const golfNetCourse: any = {
          igolf_golfnet: postScoreDto,
          tee_set_rating: teeRatting,
        };
        const golfNetScoreDto: GolfNetScoreDto = this.transformGolfNetScore(user, golfNetCourse, round, userGender);

        const score = await this.golfNetService.postScore(golfNetScoreDto);
        await this.roundRepo.update(
          { id: round.id },
          {
            golfnet_round_id: score.id,
            golfnet_course_id: score.courseId,
            golfnet_course_name: score.courseName,
            golfnet_tee_set_id: score.teeId,
            golfnet_tee_set_name: score.teeName,
          }
        );
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.errorCode,
          golfNetCourseId: error?.golfnet_course_id,
        });
      }
    }
    return {};
  }

  /**
   * transformGhinScoreHoleDetail
   *
   * @param user
   * @param ghinCourse
   * @param round
   * @param userGender
   * @returns
   */
  transformGhinScoreHoleDetail(user: User, ghinCourse: any, round: Round, userGender: string): any {
    const scoreDto: GhinScoreDto = this.transformGhinScore(user, ghinCourse, round, userGender);
    scoreDto.hole_details = this.transformGHINScoreHole(round);
    // if (this.isPlayedMoreNineHoles(scoreDto.hole_details.length)) {
    //   if (scoreDto.hole_details[0].hole_number > 1) {
    //     scoreDto.hole_details.reverse();
    //     scoreDto.hole_details.length = 9;
    //     scoreDto.hole_details.reverse();
    //   } else {
    //     scoreDto.hole_details.length = 9;
    //   }
    // }
    round.total_score = scoreDto.hole_details.reduce((sum, hole) => sum + hole.raw_score, 0);
    scoreDto.with_gps = false;
    scoreDto.with_watch = false;
    scoreDto.game_score = true;
    scoreDto.game_type = 'net_score';
    scoreDto.transferred = false;
    delete scoreDto.adjusted_gross_score;
    return scoreDto;
  }

  /**
   * isPlayedMoreNineHoles
   * @param totalHolesPlayed
   * @returns
   */
  isPlayedMoreNineHoles(totalHolesPlayed: number) {
    return totalHolesPlayed > 9 && totalHolesPlayed < 18;
  }

  /**
   * isValidPlayMoreNineHoles
   *
   * @param startHole
   * @param endHole
   * @param lstHolesNumber
   * @returns
   */
  isValidPlayMoreNineHoles(startHole: number, endHole: number, lstHolesNumber?: any) {
    if (startHole == 1 && endHole == 18) {
      if (lstHolesNumber && (lstHolesNumber.includes(9) || lstHolesNumber.includes(10))) {
        return true;
      }
      return false;
    }
    if ((startHole == 1 && endHole >= 9) || (startHole <= 10 && endHole == 18)) {
      return true;
    }
    return false;
  }

  /**
   * roundPlayInfo
   *
   * @param round
   * @returns
   */
  roundPlayInfo(round: Round) {
    let startHole = 0;
    let endHole = 0;
    let totalHolesPlayed = 0;
    let lstHolesNumber = [];
    try {
      const holesPlayed = round.holes.filter((hole: any) => +hole.total_score > 0);
      if (holesPlayed.length > 0) {
        startHole = +holesPlayed[0]['hole_number'];
        endHole = +holesPlayed[holesPlayed.length - 1]['hole_number'];
        totalHolesPlayed = holesPlayed.length;
        lstHolesNumber = holesPlayed.map((h: any) => h.hole_number);
      }
      return { startHole, endHole, totalHolesPlayed, lstHolesNumber };
    } catch (error) {
      console.error(`ERROR GET ROUND_PLAY_INFO: ${error.message}`);
      console.error(error);
      return { startHole, endHole, totalHolesPlayed, lstHolesNumber };
    }
  }

  /**
   * updateScoreToGHIN
   *
   * @param round
   */
  async updateScoreToGHIN(round) {
    try {
      const roundData = await this.roundRepo.findOne({ where: { id: round.id } });
      if (roundData && roundData.ghin_round_id) {
        const scoreUpdateDto: UpdateScoreGhinDto = {
          adjusted_gross_score: roundData.total_score.toString(),
          ghin_course_id: roundData.ghin_course_id,
          ghin_course_name: roundData.ghin_course_name,
          ghin_tee_set_id: roundData.ghin_tee_set_id,
          ghin_tee_set_name: roundData.ghin_tee_set_name,
          ghin_round_id: roundData.ghin_round_id.toString(),
          played_at: moment(roundData.played_on).format('YYYY-MM-DD'),
        };
        const scoreUpdate = await this.ghinService.updateScore(scoreUpdateDto);
        this.logger.debug(`UPDATE_GHIN_SCORE: ${JSON.stringify(scoreUpdate)}`);
      }
    } catch (error) {
      this.logger.error(`UPDATE_GHIN_SCORE FAILED: ${error.message}`);
      this.logger.error(error);
    }
  }

  /**
   * confirmPostScoreToGHIN
   *
   * @param id
   * @param postScoreDto
   * @returns
   */
  async confirmPostScoreToGHIN(id: number, postScoreDto: PostScoreDto) {
    const round: any = await this.findOneBy(id);
    if (round && !isEmpty(round)) {
      try {
        const user = await this.userRepo.findOne({
          where: { id: round.user_id },
          select: ['gender', 'id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        if (!user.ghin_id) {
          throw new UnauthorizedException(`User don't have a GHIN id`);
        }
        if (!user.ghin_gpa_status) {
          throw new UnauthorizedException(`User need to acknowledge request golfer product access!`);
        }
        let userGender = user.gender;
        if (!userGender || userGender.length > 6) {
          userGender = 'Male';
        }

        this.validateScore(round);

        const ghinCourseDetail = await this.ghinService.getGHINCourseDetail(postScoreDto.ghin_course_id);
        const ghinTeeRatingActive = ghinCourseDetail.TeeSets.filter((t) => t.TeeSetState.toLowerCase() == 'active');
        let teeMap = null;
        if (ghinTeeRatingActive.length > 0) {
          teeMap = ghinTeeRatingActive.find((tee) => {
            return tee.TeeSetRatingId == postScoreDto.tee_set_id;
          });
        }

        const convertTee = {};
        convertTee['tee_set_rating_name'] = teeMap.TeeSetRatingName;
        convertTee['ghin_tee_rating_id'] = teeMap.TeeSetRatingId;
        convertTee['gender'] = teeMap.Gender;
        convertTee['holes_number'] = teeMap.HolesNumber;
        convertTee['total_yard_age'] = teeMap.TotalYardage;

        const ghinCourse = { igolf_ghin: { ghin_course_id: postScoreDto.ghin_course_id }, tee_set_rating: convertTee };
        let ghinScoreWithHoleDetails = null;
        if ([ROUND.ROUND_MODE_ADVANCED, ROUND.ROUND_MODE_CLASSIC].includes(round.round_mode)) {
          ghinScoreWithHoleDetails = this.transformGhinScoreHoleDetail(user, ghinCourse, round, userGender);
        }
        const ghinScoreDto: GhinScoreDto = this.transformGhinScore(user, ghinCourse, round, userGender);
        const isPostHoleByHole = ghinScoreWithHoleDetails != null;
        const score = await this.ghinService.postScoreToGHIN(ghinScoreDto, ghinScoreWithHoleDetails, isPostHoleByHole);
        if (score) {
          await this.createGhinRound(score, user, { ...teeMap });
        }
        await this.roundRepo.update(
          { id: round.id },
          {
            ghin_round_id: score.id,
            ghin_course_id: score.course_id,
            ghin_course_name: score.course_display_value,
            ghin_tee_set_id: score.tee_set_id,
            ghin_tee_set_name: score.tee_name,
          }
        );
        return { success: true, score };
      } catch (error) {
        this.logger.error(error);
        throw new BadRequestException({
          message: error.message,
          errorCode: error?.errorCode,
          ghin_course_id: error?.ghin_course_id,
        });
      }
    }
    return {};
  }

  private async createGhinRound(score: any, user: User, ghinTeeSetDetail?: any) {
    let totalPars = 0;

    const teeSets = await this.ghinService.getGHINCourseHandicap(`${user.id}`, score.course_id);
    const teeSetInfo = teeSets?.tee_sets?.find((tee) => tee.tee_set_id == ghinTeeSetDetail.TeeSetRatingId);
    const teeSetRatting = teeSetInfo?.ratings?.find(
      (rating) => rating.tee_set_side?.toLowerCase()?.replace(' ', '') == score.tee_set_side.toLowerCase()
    );
    if (teeSetRatting) {
      totalPars = teeSetRatting?.par;
    }
    const totalYards = this.calculateGhinCourseYards(ghinTeeSetDetail, score);

    const scoreToPar = +score.adjusted_gross_score - totalPars;
    const ghinRoundDto: GhinRoundDto = {
      course_rating: score.course_rating,
      score: score.adjusted_gross_score,
      ghin_round_id: score.id,
      ghin_course_id: score.course_id,
      ghin_course_name: score.course_display_value,
      ghin_tee_set_id: score.tee_set_id,
      ghin_tee_set_name: score.tee_name,
      played_on: score.played_at,
      created_at: score.posted_at,
      updated_at: score.posted_at,
      ghin_id: +user.ghin_id,
      user_id: +user.id,
      slope_rating: score.slope_rating,
      score_type: score.score_type,
      ghin_parent_round_id: score.parent_id,
      ghin_tee_set_side: score.tee_set_side,
      number_of_holes_played: +score.number_of_played_holes,
      score_to_par: scoreToPar,
      generated_by: score.generated_by,
      course_par: teeSetRatting?.par || 0,
      course_yards: totalYards,
    };
    await this.ghinRoundRepo.save(ghinRoundDto);
  }

  private async createGolfNetRound(score: any, user: User, golfNetTeeSetDetail, round) {
    const { par, yards } = this.transformSlopeRatingParYardsGolfNet(golfNetTeeSetDetail, round);
    const simpleScore = score?.adjScore - par;
    const golfNetRoundDto: GolfNetRoundDto = {
      user_id: user.id,
      canada_card_id: user.canada_card_id,
      golfnet_round_id: score?.id,
      golfnet_course_id: score?.courseId,
      golfnet_course_name: score?.courseName,
      golfnet_tee_set_id: score?.teeId,
      golfnet_tee_set_name: score?.teeName,
      score: score?.adjScore,
      score_to_par: simpleScore || 0,
      generated_by: round?.generated_by || 'iOS Device',
      course_rating: score?.rating,
      slope_rating: score?.slope,
      course_yards: yards,
      number_of_holes_played: round?.number_of_holes_played,
      score_type: score?.scoreType,
      course_par: par,
      played_on: round?.played_on ? round?.played_on.toString() : score?.date,
      played_on_utc: round?.played_on_utc ? round?.played_on_utc.toString() : new Date(),
      user_timezone: round?.user_timezone,
      created_at: new Date(),
      updated_at: new Date(),
    };
    const golfNetRound = await this.golfNetRoundRepo.create(golfNetRoundDto);
    return this.golfNetRoundRepo.save(golfNetRound);
  }

  /**
   * calculateGhinCourseYards
   *
   * @param ghinTeeSetDetail
   * @param score
   * @returns
   */
  private calculateGhinCourseYards(ghinTeeSetDetail: any, score: any) {
    if (!ghinTeeSetDetail) {
      return 0;
    }
    let totalYards = ghinTeeSetDetail?.TotalYardage ?? 0;
    if (score.tee_set_side?.toUpperCase() == 'F9') {
      totalYards = 0;
      for (let i = 0; i < 9; i++) {
        totalYards += +ghinTeeSetDetail?.Holes[i].Length;
      }
      return totalYards;
    }
    if (score.tee_set_side?.toUpperCase() == 'B9' && ghinTeeSetDetail?.Holes.length == 18) {
      totalYards = 0;
      for (let i = 9; i < 18; i++) {
        totalYards += +ghinTeeSetDetail?.Holes[i].Length;
      }
      return totalYards;
    }
    return totalYards;
  }

  /**
   * validateScore
   *
   * @param round
   * @returns
   */
  validateScore(round: any) {
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      return;
    }
    const roundPlayInfo = this.roundPlayInfo(round);
    const { startHole, endHole, totalHolesPlayed, lstHolesNumber } = roundPlayInfo;
    const isPlayedMoreNineHoles = this.isPlayedMoreNineHoles(totalHolesPlayed);
    if (isPlayedMoreNineHoles) {
      if (!this.isValidPlayMoreNineHoles(startHole, endHole, lstHolesNumber)) {
        this.throwErrorNumberHolesPlayedInvalid();
      }
    } else {
      if (totalHolesPlayed == 9) {
        let endCount = 9;
        endCount = startHole == 1 ? 9 : 18;
        for (let i = startHole; i <= endCount; i++) {
          if (!lstHolesNumber.includes(i)) {
            this.throwErrorNumberHolesPlayedInvalid();
          }
        }
      }
    }
  }

  /**
   * transformGhinScore
   *
   * @param user
   * @param ghinCourse
   * @param round
   * @param userGender
   * @returns
   */
  private transformGhinScore(user: User, ghinCourse: any, round: Round, userGender: string): GhinScoreDto {
    let tee_set_side = 'All18';
    let holeNumber = ghinCourse.tee_set_rating.holes_number;
    const totalHoleNumber = holeNumber;
    const dataTee = this.transformTeeSetSide(holeNumber, tee_set_side, round);
    tee_set_side = dataTee.tee_set_side;
    holeNumber = dataTee.holeNumber;

    let totalScore = round.total_score;
    if (round.round_mode != ROUND.ROUND_MODE_SIMPLE) {
      const roundPlayInfo = this.roundPlayInfo(round);
      const { startHole, totalHolesPlayed } = roundPlayInfo;
      const isPlayedMoreNineHoles = totalHolesPlayed >= 9 && totalHolesPlayed < 18;
      ({ holeNumber, tee_set_side, totalScore } = this.transformTeeSetSideWhenPlayedMoreNineHoles(
        isPlayedMoreNineHoles,
        totalHolesPlayed,
        startHole,
        tee_set_side,
        totalScore
      ));
    } else {
      totalScore = this.getTotalScoreByRoundMode(round, totalScore);
    }

    this.validateRoundScore(holeNumber, totalScore);
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const momentTz = require('moment-timezone');
    const playedAt = momentTz(round.played_on).tz('America/Los_Angeles').format('YYYY-MM-DD');
    // const playedAt = moment(round.played_on).format('YYYY-MM-DD');
    // const playedAt = round.played_on as any;
    return {
      golfer_id: user.ghin_id,
      course_id: ghinCourse?.igolf_ghin?.ghin_course_id,
      tee_set_id: ghinCourse?.tee_set_rating?.ghin_tee_rating_id,
      tee_set_side,
      played_at: playedAt,
      score_type: round.source_type || GHIN_SCORE_TYPES.AWAY,
      number_of_holes: holeNumber == 9 ? 9 : totalHoleNumber,
      gender: userGender.toLowerCase() == 'male' ? 'M' : 'F',
      override_confirmation: true,
      is_manual: false,
      source: GHIN_SOURCE_TYPES.GHIN_COM,
      allow_duplicates: true,
      adjusted_gross_score: +totalScore,
      number_of_played_holes: holeNumber,
    };
  }

  /**
   * transformGolfNetScore
   *
   * @param user
   * @param golfNetCourse
   * @param round
   * @param userGender
   * @returns
   */
  private transformGolfNetScore(
    user: User,
    golfNetCourse: GolfNetCourseMapped,
    round: Round,
    userGender: string
  ): GolfNetScoreDto {
    const scoreDto = new GolfNetScoreDto();
    scoreDto.golfCanadaCardId = user.canada_card_id;
    scoreDto.courseId = golfNetCourse.igolf_golfnet.golfnet_course_id;
    scoreDto.courseName = golfNetCourse.igolf_golfnet.golfnet_course_name;
    scoreDto.teeId = golfNetCourse.tee_set_rating.golfnet_tee_rating_id;
    scoreDto.gender = userGender && userGender.toLowerCase() == 'female' ? 'Female' : 'Male';
    let teeDetail: any = {};
    try {
      teeDetail = JSON.parse(golfNetCourse.tee_set_rating.tee_rating_detail);
      const { rating, slope } = this.transformSlopeRatingParYardsGolfNet(teeDetail, round);
      scoreDto.slope = slope;
      scoreDto.rating = rating;
    } catch (error) {}

    const tee_set_side = 'All18';

    let holeNumber = golfNetCourse.tee_set_rating.holes_number;
    const dataTee = this.transformTeeSetSide(holeNumber, tee_set_side, round);
    holeNumber = dataTee.holeNumber;

    let totalScore = round.total_score;
    totalScore = this.getTotalScoreByRoundMode(round, totalScore);

    this.validateRoundScore(holeNumber, totalScore);
    const isTrackingStats = [ROUND.ROUND_MODE_ADVANCED, ROUND.ROUND_MODE_CLASSIC].includes(round.round_mode as ROUND);
    scoreDto.isTrackingStats = isTrackingStats;
    scoreDto.scoreType = round?.source_type || 'T';
    scoreDto.isTournament = round?.round_type?.toLowerCase() == 'tournament';
    scoreDto.isNGL = false;
    scoreDto.isPlayedAlone = round.round_mode != ROUND.ROUND_MODE_MULTIPLAYER;
    scoreDto.isHoleByHole = isTrackingStats;
    scoreDto.isManualEntry = false;
    scoreDto.holesPlayed = holeNumber == 9 ? 'NineHoles' : 'EighteenHoles';
    scoreDto.adjScore = totalScore;
    scoreDto.gross = totalScore;

    scoreDto.date = round.played_on.toString();
    return scoreDto;
  }

  /**
   * getTotalScoreToSubmit
   *
   * @param round
   * @param holeNumber
   * @param tee_set_side
   * @returns
   */
  private getTotalScoreToSubmit(round: Round, holeNumber: any, tee_set_side: string) {
    let totalScore = round.total_score;
    if (round.round_mode != ROUND.ROUND_MODE_SIMPLE) {
      const roundPlayInfo = this.roundPlayInfo(round);
      const { startHole, totalHolesPlayed } = roundPlayInfo;
      const isPlayedMoreNineHoles = totalHolesPlayed >= 9 && totalHolesPlayed < 18;
      ({ holeNumber, tee_set_side, totalScore } = this.transformTeeSetSideWhenPlayedMoreNineHoles(
        isPlayedMoreNineHoles,
        holeNumber,
        startHole,
        tee_set_side,
        totalScore
      ));
    } else {
      totalScore = this.getTotalScoreByRoundMode(round, totalScore);
    }
    return totalScore;
  }

  /**
   * transformGolfNetHoleScore
   *
   * @param round
   */
  transformGolfNetHoleScore(round: Round) {
    const holes: GolfNetScoreHoleDto[] = [];
    /*
      number": 0,
      "gross": 0,
      "adjScore": 0,
      "putts": 0,
      "puttLength": 0,
      "golfClub": "Driver",
      "drive": 0,
      "fir": "Hit",
      "upDown": true,
      "sandSave": true,
      "penalty": 0,
      "gir": true
     */
    round.holes.map((hole: any) => {
      const golfNetHole = new GolfNetScoreHoleDto();
      golfNetHole.adjScore = hole.total_score;
      if (hole.total_score > 20) {
        throw new BadRequestException({
          message: 'The hole total score must be between 1 and 20.',
          errorCode: GOLF_NET_ERROR_CODES.ERROR_POST_SCORE,
        });
      }
      golfNetHole.gross = hole.total_score;
      golfNetHole.number = hole.hole_number;
      golfNetHole.penalty = hole?.strokes?.filter((stroke) => stroke.penalty)?.length || 0;
      golfNetHole.putts =
        hole?.strokes?.filter(
          (stroke) => includeStr(stroke.starting_lie, 'green') && includeStr(stroke.ending_lie, 'green')
        )?.length || 0;
      if (round.round_mode == ROUND.ROUND_MODE_CLASSIC) {
        golfNetHole.fir = hole.fw_stats == 'fw_hit' ? 'Hit' : '';
        golfNetHole.gir = hole.gr_stats == 'gr_hit';
        golfNetHole.penalty = hole.penalty_hit ? 1 : 0;
        golfNetHole.putts = hole.putts_number;
      }
      if (hole.total_score != 0 && hole.total_score != null) {
        holes.push(golfNetHole);
      }
    });

    if (this.isPlayedMoreNineHoles(holes.length)) {
      if (holes[0].number > 1) {
        holes.reverse();
        holes.length = 9;
        holes.reverse();
      } else {
        holes.length = 9;
      }
    }
    round.total_score = holes.reduce((sum, hole) => sum + hole.gross, 0);

    return holes;
  }
  /**
   * throwErrorNumberHolesPlayedInvalid
   *
   * @param ghinCourse
   */
  private throwErrorNumberHolesPlayedInvalid(ghinCourse?: any) {
    throw new BadRequestException({
      message: `Please post a valid total 9-hole score for holes 1 thru 9 or 10 thru 18`,
      errorCode: GHIN_ERROR_CODES.ERROR_MAP_NUMBER_HOLES_PLAYED,
      ghinCourseId: ghinCourse?.igolf_ghin?.ghin_course_id,
    });
  }

  /**
   * getTotalScoreByRoundMode
   *
   * @param round
   * @param totalScore
   * @returns
   */
  private getTotalScoreByRoundMode(round: Round, totalScore: number) {
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      totalScore = 0;
      if (round.front_9_score) {
        totalScore += +round.front_9_score;
      } else if (round.back_9_score) {
        totalScore += +round.back_9_score;
      } else if (round.number_of_holes_played == 18) {
        totalScore = +round.eighteen_holes_score;
      }
    }
    return totalScore;
  }

  /**
   * transformTeeSetSideWhenPlayedMoreNineHoles
   *
   * @param isPlayedMoreNineHoles
   * @param holeNumber
   * @param startHole
   * @param tee_set_side
   * @param totalScore
   * @returns
   */
  private transformTeeSetSideWhenPlayedMoreNineHoles(
    isPlayedMoreNineHoles: boolean,
    holeNumber: any,
    startHole: number,
    tee_set_side: string,
    totalScore: number
  ) {
    if (isPlayedMoreNineHoles) {
      if (holeNumber == 9) {
        if (startHole == 1) {
          tee_set_side = 'F9';
        } else {
          tee_set_side = 'B9';
        }
      } else {
        tee_set_side = 'All18';
      }
      // if (totalScore > 90) {
      //   totalScore = 90;
      // }
    }
    return { holeNumber, tee_set_side, totalScore };
  }

  /**
   * transformTeeSetSide
   *
   * @param holeNumber
   * @param tee_set_side
   * @param round
   * @returns
   */
  transformTeeSetSide(holeNumber: any, tee_set_side: string, round: Round) {
    if (holeNumber == 9) {
      tee_set_side = 'F9';
    }
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      holeNumber = +round.number_of_holes_played || holeNumber;
      if (holeNumber == 9) {
        const isFrontNine = round.front_9_score ? true : false;
        tee_set_side = isFrontNine ? 'F9' : 'B9';
      } else {
        tee_set_side = 'All18';
      }
    }
    return { tee_set_side, holeNumber };
  }

  transformSlopeRatingParYardsGolfNet(teeDetail: any, round: Round) {
    let rating = teeDetail?.rating;
    let slope = teeDetail?.slope;
    let par = teeDetail?.par;
    let yards = teeDetail?.yardage;
    if (round.front_9_score) {
      rating = teeDetail?.frontRating;
      slope = teeDetail?.frontSlope;
      par = teeDetail?.frontPar;
      yards = teeDetail?.frontYardage;
    } else if (round.back_9_score) {
      rating = teeDetail?.backRating;
      slope = teeDetail?.backSlope;
      par = teeDetail?.backPar;
      yards = teeDetail?.backYardage;
    }
    return { rating, slope, par, yards };
  }

  /**
   * transformGHINScoreHole
   *
   * @param round
   * @returns
   */
  transformGHINScoreHole(round: any) {
    const holes = [];
    for (const hole of round.holes) {
      if (hole.total_score == 0) {
        continue;
      }
      const ghinScoreHole: GhinScoreHoleDto = {
        hole_number: hole.hole_number,
        raw_score: hole.total_score,
        par: hole.par,
      };
      if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
        const putts = hole.strokes.filter(
          (stroke) => includeStr(stroke.starting_lie, 'green') && includeStr(stroke.ending_lie, 'green')
        );
        ghinScoreHole.putts = putts.length;
      }
      if (round.round_mode == ROUND.ROUND_MODE_CLASSIC) {
        const isFWHit = hole.fw_stats ? hole.fw_stats == 'fw_hit' : null;
        const isGRHit = hole.gr_stats ? hole.gr_stats == 'gr_hit' : null;
        ghinScoreHole['fairway_hit'] = isFWHit;
        ghinScoreHole['gir_flag'] = isGRHit;
        ghinScoreHole['putts'] = hole.putts_number || 0;
        if (isFWHit === false) {
          ghinScoreHole['drive_accuracy'] =
            hole.fw_stats == 'fw_missed_left' ? DRIVE_ACCURACY.MISSED_LEFT : DRIVE_ACCURACY.MISSED_RIGHT;
        }
        if (isGRHit === false) {
          switch (hole.gr_stats) {
            case 'gr_missed_left':
              ghinScoreHole['approach_shot_accuracy'] = APPROACH_SHOT_ACCURACY.MISSED_LEFT;
              break;
            case 'gr_missed_right':
              ghinScoreHole['approach_shot_accuracy'] = APPROACH_SHOT_ACCURACY.MISSED_RIGHT;
              break;
            case 'gr_missed_long':
              ghinScoreHole['approach_shot_accuracy'] = APPROACH_SHOT_ACCURACY.MISSED_LONG;
              break;
            case 'gr_missed_short':
              ghinScoreHole['approach_shot_accuracy'] = APPROACH_SHOT_ACCURACY.MISSED_SHORT;
              break;
          }
        }
      }
      holes.push(ghinScoreHole);
    }
    return holes;
  }

  /**
   * transformResponseRound
   *
   * @param round
   * @returns
   */
  async transformResponseRound(round: any) {
    const roundScore = this.getScore(round);
    const json = {};
    const statsCompleted = await this.isStatsCompleted(round, roundScore);

    json['device_token'] = round.device_token;
    json['id'] = round.id;
    json['user_id'] = round.user_id;
    json['course_id'] = round.course_id;
    json['igolf_course_id'] = round.igolf_course_id;
    json['map_id'] = round.map_id;
    json['course_name'] = round.course_name;
    json['tee_name'] = round.tee_name;
    json['tee_name_display'] = round.tee_name;
    json['generated_by'] = round.generated_by;
    json['created_at'] = formatDate(round.created_at);
    json['played_on'] = this.playedOnDate(round);
    json['round_type'] = round.round_type;
    json['round_mode'] = round.round_mode;
    json['course_yards'] = round.course_yards;
    json['course_par'] = round.course_par;
    json['deleted'] = round.deleted_at || '';
    json['completed'] = round.completed;
    json['stats_completed'] = statsCompleted;
    json['inprogress'] = round.inprogress;
    json['course_conditions'] = {
      ground_type: round.ground_conditions,
      temp: round.temperature,
    };
    json['team'] = round.team;
    json['multiplayer_game_type'] = round.multiplayer_game_type;
    json['play_service'] = round.play_service;
    json['play_client'] = round.play_client;
    json['ghin_round_id'] = round.ghin_round_id;
    json['ghin_score'] = round.ghin_score;
    json['arccos_round_id'] = round.arccos_round_id;
    json['ghin_course_id'] = round.ghin_course_id;
    json['ghin_course_name'] = round.ghin_course_name;
    json['ghin_tee_set_id'] = round.ghin_tee_set_id;
    json['ghin_tee_set_name'] = round.ghin_tee_set_name;
    json['golfnet_round_id'] = round.golfnet_round_id;
    json['source_type'] = round.source_type;

    this.transformRoundModeSimple(round, json);
    await this.transformRoundModeMultiplayer(round, json);

    let scoreToPar = 0;
    let totalHoleScore = 0;
    json['holes'] = [];

    if (round.holes) {
      const lstHoleScores: any = await this.holesPlayedService.getScoreListHole(
        round.round_mode,
        round.holes,
        round.id
      );

      for (const hole of round.holes) {
        const holeData = {};

        const resultHoleScore = lstHoleScores.find((h: any) => +h.id == +hole.id);

        const hole_score = resultHoleScore?.score || 0;

        if (hole_score > 0) {
          totalHoleScore += +hole_score;
          scoreToPar += +hole_score - +hole.par;
        }
        let pin_location = [0, 0];
        if (hole?.pin_location) {
          pin_location = [hole?.pin_location?.coordinates[1], hole?.pin_location?.coordinates[0]];
        }
        holeData['id'] = hole.id;
        holeData['hole_number'] = +hole.name;
        holeData['par'] = hole.par;
        holeData['yards'] = hole.yards;
        holeData['handicap'] = hole.handicap_index || hole.stroke_index;
        holeData['total_score'] = hole_score;
        holeData['started_on'] = hole.created_at;
        holeData['shots_removed'] = hole.shots_removed;
        holeData['png_image_url'] = hole.image_url;
        holeData['svg_image_url'] = hole.svg_image_url;
        holeData['pin_location'] = pin_location;

        this.transformHoleDataRoundModeClassic(round, holeData, hole);

        this.transformHolePlayerMetaData(holeData, hole);
        if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
          await this.transformPlayerScores(hole, holeData);
        }
        if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
          const queryAllStroke = this.strokePlayerRepo.createQueryBuilder('SP');
          queryAllStroke.where({ round_id: round.id });
          queryAllStroke.select([
            'id',
            'stats.shot_distance AS shot_distance',
            'stats.ending_distance_to_pin AS ending_distance_to_pin',
            'stats.ending_lie',
            'hole_played_id',
            'club_id',
            'ordinal',
            'timestamp',
            'lie',
            'ST_AsGeoJSON("coords")::json AS coords',
            'penalty',
            'difficult',
            'recovery',
            'manually_moved',
            'auto_moved',
            'manually_added',
          ]);
          queryAllStroke.leftJoin(
            (subQuery) => {
              return subQuery
                .from(StrokeStat, 'stats')
                .select(['shot_distance', 'ending_distance_to_pin', 'stroke_played_id', 'ending_lie']);
            },
            'stats',
            'stats.stroke_played_id = SP.id'
          );

          const lstCDMWitbIds = await this.getListCDMWitbIds(round);
          const allStrokes = await queryAllStroke.getRawMany();
          holeData['strokes'] = await this.transformHoleStrokes(hole, lstCDMWitbIds, allStrokes, round.user_id);
          holeData['strokes'] = deleteValueBlank(holeData['strokes']);
        }

        const prepareHole = deleteValueBlank(holeData);
        json['holes'].push(prepareHole);
      }
    }
    json['holes'] = json['holes'].sort((a, b) => +a.hole_number - +b.hole_number);
    let totalScore: any = 0;
    if (
      round.round_mode == ROUND.ROUND_MODE_BASIC ||
      round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER ||
      round.round_mode == ROUND.ROUND_MODE_CLASSIC ||
      round.round_mode == ROUND.ROUND_MODE_ADVANCED
    ) {
      totalScore = totalHoleScore + '';
      json['total_score'] = totalScore;
    } else {
      totalScore = roundScore + '';
      json['total_score'] = roundScore + '';
    }

    this.transformRoundPlayerMetaData(round, json, totalScore);

    json['score_to_par'] = round.round_mode == ROUND.ROUND_MODE_SIMPLE ? round.simple_score_to_par : scoreToPar;
    return json;
  }

  /**
   * transformPlayerScores
   *
   * @param hole
   * @param holeData
   */
  private async transformPlayerScores(hole: any, holeData: any) {
    const playerScores = await this.playerService.findPlayedScoreByHole(hole.id);
    holeData['player_score'] = [];
    if (playerScores) {
      for (const score of playerScores) {
        const pScore = {
          hole_score: score.hole_score,
          player_id: score.player_id,
        };
        holeData['player_score'].push(pScore);
      }
    }
  }

  /**
   * isStatsCompleted
   *
   * @param round
   * @param totalScore
   * @returns
   */
  private async isStatsCompleted(round: any, totalScore: any) {
    let statsCompleted = await this.statsCompletedProcessing(round);
    if (totalScore == 0 && statsCompleted == false && !!round.completed) {
      statsCompleted = true;
    }
    if (
      round.round_mode == ROUND.ROUND_MODE_BASIC ||
      round.round_mode == ROUND.ROUND_MODE_SIMPLE ||
      round.round_mode == ROUND.ROUND_MODE_CLASSIC
    ) {
      statsCompleted = true;
    }
    return statsCompleted;
  }

  /**
   * transformRoundPlayerMetaData
   *
   * @param round
   * @param json
   * @param totalScore
   */
  private transformRoundPlayerMetaData(round: any, json: any, totalScore) {
    const data = {
      event_id: round.event_id,
      total_score: totalScore + '',
      duration: round.duration,
      calories_burned: round.calories_burned,
      average_heartbeat: round.average_heartrate,
      peak_heartbeat: round.peak_heartrate,
      lowest_heartbeat: round.lowest_heartrate,
      steps_count: round.steps_count,
      user_timezone: round.user_timezone,
    };
    json['player_metadata'] = deleteValueBlank(data);
  }

  /**
   * transformRoundModeSimple
   *
   * @param round
   * @param json
   */
  private transformRoundModeSimple(round: any, json: any) {
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      json['number_of_holes_played'] = round.number_of_holes_played;
      json['front_9_score'] = round.front_9_score;
      json['back_9_score'] = round.back_9_score;
      json['eighteen_holes_score'] = round.eighteen_holes_score;
    }
  }

  /**
   * transformRoundModeMultiplayer
   *
   * @param round
   * @param json
   */
  private async transformRoundModeMultiplayer(round: any, json: any) {
    if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
      const players = await this.playerService.findBy({ where: { round_id: round.id } });
      json['player'] = [];
      if (players) {
        for (const player of players) {
          const playerData = {};
          playerData['player_name'] = player.player_name;
          playerData['player_initials'] = player.player_initials;
          playerData['stroke_received'] = player.stroke_received;
          playerData['team'] = player.team;
          playerData['user_id'] = player.user_id;
          playerData['player_id'] = player.player_id;
          playerData['player_email'] = player.player_email;
          json['player'].push(playerData);
        }
      }
    }
  }

  /**
   * getListCDMWitbIds
   *
   * @param round
   * @returns
   */
  private async getListCDMWitbIds(round: any) {
    let clubIdsRound;
    const query = this.strokePlayerRepo.createQueryBuilder();
    query.select(['DISTINCT(club_id) as club_id']);
    query.where({ round_id: round.id });
    clubIdsRound = await query.getRawMany();
    if (clubIdsRound) {
      clubIdsRound = clubIdsRound.map((c) => c.club_id);
    }
    return (await this.clubsService.findLstCdmWitbId(clubIdsRound)) || [];
  }

  /**
   * transformHoleDataRoundModeClassic
   *
   * @param round
   * @param holeData
   * @param hole
   */
  private transformHoleDataRoundModeClassic(round: any, holeData: any, hole: any) {
    if (round.round_mode == ROUND.ROUND_MODE_CLASSIC) {
      holeData['fw_stats'] = hole.fw_stats;
      holeData['gr_stats'] = hole.gr_stats;
      holeData['putts_number'] = hole.putts_number;
      holeData['bunker_hit'] = hole.bunker_hit;
      holeData['penalty_hit'] = hole.penalty_hit;
    }
  }

  /**
   * transformHolePlayerMetaData
   *
   * @param holeData
   * @param hole
   */
  private transformHolePlayerMetaData(holeData: any, hole: any) {
    holeData['player_metadata'] = {
      lowest_heartrate: hole.lowest_heartrate,
      average_heartrate: hole.average_heartrate,
      peak_heartrate: hole.peak_heartrate,
      calories_burned: hole.calories_burned,
      steps_count: hole.steps_count,
    };
    holeData['player_metadata'] = deleteValueBlank(holeData['player_metadata']);
  }

  /**
   * transformHoleStrokes
   *
   * @param hole
   * @param lstCDMWitbIds
   * @param allStrokes
   * @param user_id
   * @returns
   */
  private async transformHoleStrokes(hole: any, lstCDMWitbIds: any, allStrokes: any, user_id: number) {
    let strokes = [];
    let holeStrokes = allStrokes.filter((stroke) => stroke.hole_played_id == hole.id);
    if (holeStrokes) {
      holeStrokes = _.sortBy(holeStrokes, 'ordinal');
      const strokeIds = holeStrokes.map((stroke) => stroke.id);
      const strokeGained = await this.strokePlayedService.strokes_gained_from_stats(strokeIds, user_id);
      // check duplicate strokes
      const groupByStrokesId = _.groupBy(strokeGained, 'stroke_played_id');
      holeStrokes = await this.checkDuplicateStrokeStats(groupByStrokesId, holeStrokes);
      for (const stroke of holeStrokes) {
        const strokeData = {};
        const sg = strokeGained.find((sg) => sg.stroke_played_id == stroke.id) || { sg: 0 };
        const clubCdmWitbId = lstCDMWitbIds.find((club: any) => club.id == stroke.club_id);
        const clubId = clubCdmWitbId ? clubCdmWitbId['cdm_witb_id'] : stroke.club_id;

        strokeData['id'] = stroke.id;
        strokeData['cdm_witb_id'] = clubId;
        strokeData['club_id'] = stroke.club_id;
        strokeData['strokes_gained'] = sg['sg'] || 0;
        strokeData['shot_number'] = +stroke.ordinal;
        strokeData['shot_at'] = stroke.timestamp;
        strokeData['starting_lie'] = _.startCase(stroke.lie);
        strokeData['ending_lie'] = _.startCase(this.strokePlayedService.lie({ lie: stroke.ending_lie })); //_.startCase(this.strokePlayedService.endLie(holeStrokes, stroke));
        if (stroke.coords) {
          strokeData['coordinates'] = [stroke.coords.coordinates[1], stroke.coords.coordinates[0]];
        } else {
          strokeData['coordinates'] = [0, 0];
        }

        strokeData['penalty'] = stroke.penalty;
        strokeData['difficult'] = stroke.difficult;
        strokeData['recovery'] = stroke.recovery;
        strokeData['manually_moved'] = stroke.manually_moved;
        strokeData['auto_moved'] = stroke.auto_moved;
        strokeData['manually_added'] = stroke.manually_added;

        strokeData['shot_distance'] = stroke.shot_distance;
        strokeData['distance_to_pin'] = this.strokePlayedService.distanceToPinInMeters(stroke, hole);
        strokeData['stat_distance_to_pin'] = stroke.ending_distance_to_pin;

        strokes.push(strokeData);
      }
    }
    if (strokes) {
      strokes = strokes.sort((a, b) => +a.shot_number - +b.shot_number);
    }

    return strokes;
  }

  /**
   * checkDuplicateStrokeStats
   *
   * @param groupByStrokesId
   * @param holeStrokes
   * @returns
   */
  private async checkDuplicateStrokeStats(groupByStrokesId: any, holeStrokes: any) {
    let isDuplicateStats = false;
    const lstStrokeStatsId = Object.keys(groupByStrokesId);
    let listStatsRemoved = [];
    for (const sStatsId of lstStrokeStatsId) {
      if (groupByStrokesId[sStatsId].length > 1) {
        isDuplicateStats = true;
        this.logger.debug(`Duplicate stats found: ${sStatsId}`);
        for (let i = 1; i < groupByStrokesId[sStatsId].length; i++) {
          const statsId = groupByStrokesId[sStatsId][i]['id'];
          try {
            await this.strokesStatsService.remove(statsId);
            this.logger.debug(`Remove success stats: ${sStatsId}`);
            listStatsRemoved.push(+sStatsId);
          } catch (error) {
            this.logger.error(`Remove fail stats: ${sStatsId}`);
          }
        }
      }
    }
    if (isDuplicateStats) {
      const lstStrokeNoDuplicate = holeStrokes.filter((s) => !listStatsRemoved.includes(s.id));
      const listStrokeDuplicate = [];
      listStatsRemoved = _.uniq(listStatsRemoved);
      for (const statId of listStatsRemoved) {
        listStrokeDuplicate.push(holeStrokes.find((s) => statId == s.id));
      }
      holeStrokes = [...listStrokeDuplicate, ...lstStrokeNoDuplicate];
      holeStrokes = _.sortBy(holeStrokes, 'ordinal');
    }
    return holeStrokes;
  }

  /**
   * statsCompletedProcessing
   *
   * @param round
   * @param wait
   * @returns
   */
  async statsCompletedProcessing(round: any, wait = false) {
    if (!round) {
      return false;
    }
    if (round?.round_mode != ROUND.ROUND_MODE_ADVANCED) {
      return true;
    }
    if (round?.stats_completed == null) {
      if (wait) {
        const isCompleted = await this.updateRoundStatsCompleted({ ...round });
        return isCompleted;
      } else {
        this.updateRoundStatsCompleted({ ...round });
      }
    }
    return round.stats_completed == true;
  }

  /**
   * updateRoundStatsCompleted
   *
   * @param round
   * @returns
   */
  private async updateRoundStatsCompleted(round: any) {
    const statCount = await this.strokesStatsService.countStrokesBy({
      round_id: round.id,
    });

    let strokes = [];
    let holeIds = [];
    if (round.holes == undefined) {
      const holes = await this.holesPlayedService.getHolesOfRoundIds([round.id]);
      if (holes) {
        holeIds = holes.map((hole) => hole.id);
      }
      if (holeIds.length > 0) {
        strokes = await this.holesPlayedService.getStrokesPlayedListHole(holeIds, false, false);
      }
    } else {
      if (round.holes[0].strokes) {
        strokes = this.strokes(round);
      } else {
        holeIds = round.holes.map((hole) => hole.id);
        if (holeIds.length > 0) {
          strokes = await this.holesPlayedService.getStrokesPlayedListHole(holeIds, false, false);
        }
      }
    }

    const strokeCount = strokes.length > 0 ? strokes.length : 1;

    const percentage = (statCount / strokeCount) * 100;
    const isCompleted = percentage >= 99;
    if (isCompleted) {
      this.logger.debug('UPDATE STATS_COMPLETED TRUE ROUND:', round.id);
      this.roundRepo.update({ id: round.id }, { stats_completed: true });
    } else {
      this.logger.debug('UPDATE STATS_COMPLETED FALSE ROUND:', round.id);
      this.roundRepo.update({ id: round.id }, { stats_completed: false });
    }

    return isCompleted;
  }

  /**
   * strokes
   *
   * @param round
   * @returns
   */
  strokes(round: any) {
    const strokes = [];
    if (round.holes) {
      round.holes.forEach((hole: any) => {
        strokes.push(...hole.strokes);
      });
    }
    return strokes;
  }

  /**
   * getScore
   *
   * @param round
   * @returns
   */
  getScore(round: any) {
    let score = 0;
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      if (round.number_of_holes_played == 9) {
        return round.front_9_score || round.back_9_score;
      }
      return round.eighteen_holes_score;
    } else {
      let holesScore = 0;
      round.holes?.forEach((hole: any) => {
        holesScore += +hole.score;
      });
      score = holesScore;
    }
    return score;
  }

  /**
   * scoreHolesPlayed
   *
   * @param round
   * @returns
   */
  scoreHolesPlayed(round: any) {
    const holes = round.holes;
    if (!holes || holes.length == 0) {
      return [0, 0];
    }
    let score = 0;
    let holePlayed = 0;
    holes.forEach((hole: any) => {
      score += +hole.score;
      if (+hole.score > 0) holePlayed += 1;
    });
    return [score, holePlayed];
  }

  /**
   * playedOnDate
   *
   * @param round
   * @returns
   */
  playedOnDate(round: any) {
    const time = round.played_on || round.created_at;
    try {
      return formatDate(time);
    } catch (error) {
      return time;
    }
  }

  async find(options: any) {
    return this.roundRepo.find({
      ...options,
    });
  }
  async findRound(id: number) {
    return await this.roundRepo.findOneBy({ id });
  }
  async update(id: number, roundDto: CreateRoundDto, forceMapCourse = null) {
    let params: any = roundDto.round;
    let round = await this.findRound(id);
    if (!round) {
      return {};
    }
    let isRoundCompleted = false;
    //For Android
    if (!params && roundDto.completed != null) {
      if (roundDto.completed) {
        try {
          this.averageScoreQueue.add(
            PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE,
            {
              userId: round.user_id,
            },
            OPTIONS_JOB_DEFAULT
          );
        } catch (error) {
          console.log(`ADD QUEUE CALCULATE_AVERAGE_SCORE FAIL`);
          console.log(error);
        }

        await this.forceCheckRoundStats(id);
      }
      const isCompleted = roundDto.completed == true;
      const payloadUpdate = this.getPayloadRoundCompleted(isCompleted);
      await this.roundRepo.update({ id: id }, payloadUpdate);
      const roundUpdate = await this.findRound(id);
      roundUpdate.completed = payloadUpdate.completed;
      roundUpdate.inprogress = payloadUpdate.inprogress;
      return roundUpdate;
    }
    const holes = params.holes;
    params = this.forceCompletedTrue(params);
    if (!holes || isEmpty(holes)) {
      delete params.players;
      delete params.holes;
      let payload = { ...params };
      payload = deleteValueBlank(payload);
      if (payload.completed == 'true' || payload.completed == true) {
        payload['inprogress'] = false;
      }
      if (payload['played_on']) {
        const isValidDate = moment(payload['played_on']).isValid();
        if (!isValidDate) {
          payload['played_on'] = new Date();
        }
      }
      delete payload.map_id;
      await this.roundRepo.update({ id, user_id: round.user_id }, { ...payload });
      if (params.completed == true || params['completed']) {
        isRoundCompleted = true;
        await this.forceCheckRoundStats(round.id);
      }

      if (isRoundCompleted) {
        if (round.round_mode != ROUND.ROUND_MODE_ADVANCED) {
          try {
            this.averageScoreQueue.add(
              PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE,
              {
                userId: round.user_id,
              },
              OPTIONS_JOB_DEFAULT
            );
          } catch (error) {
            console.log(`ADD QUEUE CALCULATE_AVERAGE_SCORE FAIL`);
            console.log(error);
          }
        }
      }

      if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
        try {
          this.simpleScoreToParQueue.add(
            PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR,
            {
              roundId: round.id,
            },
            OPTIONS_JOB_DEFAULT
          );
        } catch (error) {
          console.log(`ADD QUEUE SIMPLE_SCORE_TO_PAR FAIL`);
          console.log(error);
        }

        // TODO: Pending for account Admin
        // this.updateScoreToGHIN(round);
      }
      // return await this.findOneBy(round.id);
    } else {
      let dataRound = { ...params };
      delete dataRound.holes;
      delete dataRound.user_id;
      delete dataRound.id;
      delete dataRound.players;
      delete dataRound.round_mode;
      delete dataRound.map_id;
      isRoundCompleted = dataRound.completed == true || dataRound.completed == 'true';

      if (dataRound['played_on']) {
        const isValidDatePlayedOn = moment(dataRound['played_on']).isValid();
        if (!isValidDatePlayedOn) {
          dataRound['played_on'] = new Date();
        }
      }

      let roundType = null;
      let isUpdateRoundType = false;
      if (Object.keys(dataRound).includes('round_type')) {
        isUpdateRoundType = true;
        roundType = dataRound['round_type'];
      }
      dataRound = deleteValueBlank(dataRound);
      if (isUpdateRoundType) {
        dataRound['round_type'] = roundType;
      }
      if (isRoundCompleted) {
        dataRound['inprogress'] = false;
      } else {
        dataRound['inprogress'] = true;
      }

      await this.roundRepo.update(
        { id, user_id: round.user_id },
        { ...dataRound, stats_completed: false, driving_stat_complete: false }
      );
      await sleeps(1); // for sync data between database read and write.
      round = await this.findRound(id);
      const generated_by = params?.generated_by;
      const isSubmitMultipleHoles = params?.holes?.length > 3;
      try {
        if (isGear(generated_by) && isSubmitMultipleHoles) {
          await this.roundUpdateQueue.add(
            PROCESS_QUEUE_NAMES.ROUND_UPDATE,
            { id, round, params },
            { ...OPTIONS_JOB_DEFAULT, removeOnComplete: true }
          );
          // this.roundAuditUpdate.updateRound(round, params);
        } else {
          await this.roundAuditUpdate.updateRound(round, params, forceMapCourse);
        }
      } catch (error) {
        console.log(`ADD QUEUE ROUND_UPDATE FAIL`);
        console.log(error);
      }
    }

    //  # Trigger End round to Klaviyo
    // if (params.completed == true) {
    // const userRound = await this.userRepo.findOneBy({ id: round.user_id });
    // this.myTMService.triggerEndPlayRoundKlaviyo(round, userRound.email);
    // }

    const payloadUpdate = this.getPayloadRoundCompleted(isRoundCompleted);
    const roundUpdate: any = await this.findOneBy(id);
    roundUpdate.completed = payloadUpdate.completed;
    roundUpdate.inprogress = payloadUpdate.inprogress;
    return roundUpdate;

    // # @round = RoundAuditUpdate.process_update!( @round, params[:round] )
    // if ((@round.completed == false) && (completed == true))
    //   # @round.update_attribute(:completed, false)
    //   # when a round is set to incomplete, remove the stats for that round
    //   session['review_round'] = true
    //    #StrokeStat.where(round_id: @round.id).delete_all
    // end
  }

  /**
   * getPayloadRoundCompleted
   *
   * @param isRoundCompleted
   * @returns
   */
  private getPayloadRoundCompleted(isRoundCompleted: boolean) {
    return isRoundCompleted ? { completed: true, inprogress: false } : { completed: false, inprogress: true };
  }

  private async forceCheckRoundStats(roundId) {
    const data = {
      roundId: roundId,
      isCheckStats: true,
    };
    const optionJob = { ...OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND };
    optionJob.delay = 10 * 1000 * 60;
    try {
      await this.forceRoundCompleteJobQueue.add(PROCESS_QUEUE_NAMES.FORCE_ROUND_COMPLETE, data, optionJob);
    } catch (error) {
      console.log(`ADD QUEUE FORCE_ROUND_COMPLETE FAIL`);
      console.log(error);
    }
  }

  /**
   * triggerCalculateDrivingDispersion
   *
   * @param data
   */
  async triggerCalculateDrivingDispersion(data) {
    await this.iGolfRoundDrivingDispersionJobQueue.add(
      PROCESS_QUEUE_NAMES.IGOLF_ROUND_DRIVING_DISPERSION,
      data,
      OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND
    );
  }

  /**
   * triggerCalculateAvgScore
   *
   * @param userId
   */
  async triggerCalculateAvgScore(userId) {
    await this.averageScoreQueue.add(PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE, userId, OPTIONS_JOB_DEFAULT);
  }

  /**
   * shouldAddToQueue
   *
   * @param round
   * @returns
   */
  private async shouldAddToQueue(round: Round) {
    const lstJobWaiting = await this.iGolfRoundCompleteJobQueue.getWaiting();
    const lstJobActive = await this.iGolfRoundCompleteJobQueue.getActive();
    const lstJobRoundCompleted = [...lstJobWaiting, ...lstJobActive];
    const jobsOfRoundStatusCompleted = lstJobRoundCompleted.filter(
      (job) => job.data.roundId == round.id && !job.data.holesPlayedModify
    );
    const haveJobRunUpdateHole = lstJobRoundCompleted.some(
      (job) => job.data.roundId == round.id && job.data.holesPlayedModify
    );
    if (jobsOfRoundStatusCompleted) {
      for (const job of lstJobRoundCompleted) {
        await job.moveToFailed({ message: 'Force Move Fail' }, true);
      }
    }
    return !haveJobRunUpdateHole;
  }

  /**
   * forceCompletedTrue
   *
   * @param round
   * @returns
   */
  private forceCompletedTrue(round: any) {
    if (
      [
        ROUND.ROUND_MODE_SIMPLE.toString(),
        // ROUND.ROUND_MODE_CLASSIC.toString(),
        // ROUND.ROUND_MODE_BASIC.toString(),
      ].includes(round.round_mode)
    ) {
      round.completed = true;
    }
    return round;
  }

  async remove(id: number) {
    try {
      const round = await this.roundRepo.findOne({ where: { id: id }, select: ['user_id', 'id', 'ghin_round_id'] });
      if (!round) {
        this.logger.log(`Not found round`);
        return { success: false };
      }

      await this.roundRepo.update({ id }, { deleted_at: new Date().toISOString() });
      try {
        await this.averageScoreQueue.add(
          PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE,
          {
            userId: round.user_id,
          },
          OPTIONS_JOB_DEFAULT
        );
      } catch (error) {
        console.log(`ADD QUEUE CALCULATE_AVERAGE_SCORE FAIL`);
        console.log(error);
      }

      return { success: true };
    } catch (error) {
      this.logger.log(error.message);
      return { success: false };
    }
  }

  /**
   * processCreateRound
   *
   * @param auditId
   * @param roundAuditData
   * @param forceMapCourse
   * @returns
   */
  async processCreateRound(auditId: number, roundAuditData?: any, forceMapCourse = null) {
    this.logger.debug(`PROCESS CREATE ROUND AUDIT ID: ${auditId}`);
    const roundAudit = await this.roundAuditRepo.findOne({ where: { id: auditId } });
    try {
      let data = null;
      if (roundAudit) {
        data = roundAudit.data;
      } else {
        data = roundAuditData;
      }
      const rawRound = await this.roundAuditImportMobileService.processImportRound(JSON.parse(data), forceMapCourse);
      await this.checkRoundForErrors(auditId, rawRound);
      return rawRound;
    } catch (error) {
      console.log(error);
      this.logger.error(`${auditId}: ${error}`);
      await this.roundAuditRepo.update({ id: auditId }, { process_error: error.message });
      return null;
    }
  }

  /**
   * checkRoundForErrors
   *
   * @param roundAuditId
   * @param rawRound
   */
  async checkRoundForErrors(roundAuditId, rawRound) {
    if (rawRound) {
      await this.recordRoundId(roundAuditId, rawRound);
    } else {
      await this.recordRoundErrors(roundAuditId, rawRound);
    }
  }

  /**
   * recordRoundErrors
   *
   * @param roundAuditId
   * @param rawRound
   */
  async recordRoundErrors(roundAuditId, rawRound) {
    await this.roundAuditRepo.update({ id: roundAuditId }, { error: rawRound.errors });
    // audit.update_attribute(:error, raw_round.errors.inspect)
  }

  /**
   * recordRoundId
   *
   * @param roundAuditId
   * @param rawRound
   */
  async recordRoundId(roundAuditId, rawRound) {
    await this.roundAuditRepo.update({ id: roundAuditId }, { round_id: rawRound.id });
    // audit.update_attribute(:round_id, raw_round.id)
  }

  /**
   * iGolfStatSql
   * @param roundId
   */
  async iGolfStatSql(roundId: number) {
    const sql = `
    UPDATE
        strokes_played
      SET
        result_from_pin = COALESCE(ST_Distance(second_stroke.coords, hole.pin_location), 0),
        distance = COALESCE(ST_Distance(first_stroke.coords, second_stroke.coords), ST_Distance(first_stroke.coords, hole.pin_location)),
        final_stroke = (second_stroke.id IS NULL)
      FROM
        strokes_played first_stroke LEFT OUTER JOIN
        strokes_played second_stroke ON (first_stroke.hole_played_id = second_stroke.hole_played_id AND
        second_stroke.ordinal = first_stroke.ordinal + 1) JOIN
        holes_played hole ON (hole.id = first_stroke.hole_played_id)
      WHERE
        strokes_played.id = first_stroke.id
        AND hole.round_id = ${roundId}
    `;
    await this.strokePlayerRepo.query(sql);
  }
  async statSql(roundId) {
    const sql = `
    UPDATE
    	strokes_played
    SET
      azimuth_to_pin = ST_Azimuth( hole.pin_location::geometry, first_stroke.coords::geometry),
      shot_azimuth = COALESCE(ST_Azimuth(first_stroke.coords::geometry, second_stroke.coords::geometry), ST_Azimuth(first_stroke.coords::geometry, hole.pin_location::geometry)),
    	result_from_pin = COALESCE(ST_Distance(second_stroke.coords, hole.pin_location), 0),
      result_angle_from_pin = ST_Azimuth(hole.pin_location::geometry, second_stroke.coords::geometry),
      subsequent_lie = (SELECT lie FROM strokes_played next_stroke WHERE next_stroke.hole_played_id = strokes_played.hole_played_id AND next_stroke.ordinal = strokes_played.ordinal + 1 limit 1),
      distance = COALESCE(ST_Distance(first_stroke.coords, second_stroke.coords), ST_Distance(first_stroke.coords, hole.pin_location)),
      final_stroke = (second_stroke.id IS NULL),
    	deviance = COALESCE(
    		LEAST(ST_Distance(
                    second_stroke.coords,
                    ST_MakeLine(ST_Centroid(fairway.geometric_shape), first_stroke.coords::geometry)),
                    ST_Distance(second_stroke.coords,
                    ST_MakeLine(ST_Centroid(fairway.geometric_shape), hole.pin_location::geometry))),
                0),
      center_angle = COALESCE(LEAST(
    	  ST_Azimuth(
            first_stroke.coords::geometry,
            ST_Centroid(fairway.geometric_shape)),
          ST_Azimuth(
            ST_Centroid(fairway.geometric_shape),
            hole.pin_location)),
            0),
            distance_from_pin = ST_Distance(first_stroke.coords, hole.pin_location)
    FROM
    	    strokes_played first_stroke LEFT OUTER JOIN
          strokes_played second_stroke ON (first_stroke.hole_played_id = second_stroke.hole_played_id AND
          second_stroke.ordinal = first_stroke.ordinal + 1) JOIN
          holes_played hole ON (hole.id = first_stroke.hole_played_id) JOIN
          polygons fairway USING (hole_id)
    WHERE
    	strokes_played.id = first_stroke.id
      AND hole.round_id = ${roundId} AND
      fairway.polygon_type_id = 3
    `;
    await this.strokePlayerRepo.query(sql);
  }

  /**
   * countAdvancedRoundsWithDates
   *
   * @param fromDate
   * @param toDate
   * @param userId
   * @returns
   */
  async countAdvancedRoundsWithDates(fromDate: Date, toDate: Date, userId: number) {
    return this.roundRepo.count({
      where: {
        user_id: userId,
        deleted_at: IsNull(),
        round_mode: ROUND.ROUND_MODE_ADVANCED,
        completed: true,
        created_at: Between(fromDate, toDate),
      },
      loadEagerRelations: false,
    });
  }

  /**
   * getRoundsForCalculatingHandicap
   *
   * @param userId
   * @param limit
   * @returns
   */
  async getRoundsForCalculatingHandicap(userId: number, limit) {
    console.log(`this.roundRepo: ${this.roundRepo}`);
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: userId,
      deleted_at: IsNull(),
      completed: true,
      round_mode: Not(ROUND.ROUND_MODE_MULTIPLAYER),
      igolf_course_id: Not(''),
    });
    query.select([
      'id',
      'igolf_course_id',
      'tee_name',
      'round_mode',
      'number_of_holes_played',
      'front_9_score',
      'back_9_score',
      'eighteen_holes_score',
      'played_on',
      'created_at',
    ]);
    query.orderBy({
      played_on: 'DESC',
      created_at: 'DESC',
    });
    query.take(limit);
    return await query.getRawMany();
  }

  /**
   * getUserTotalRounds
   *
   * @param userId
   * @returns
   */
  async getUserTotalRounds(userId: number) {
    const completeProcessingRounds = [];
    const rounds = await this.roundRepo.find({
      where: { user_id: userId, deleted_at: IsNull(), completed: true },
      select: ['id', 'round_mode', 'stats_completed'],
    });
    for (const round of rounds) {
      const hasCompleteProcessing = await this.statsCompletedProcessing(round);
      if (hasCompleteProcessing) {
        completeProcessingRounds.push(round);
      }
    }
    return completeProcessingRounds.length;
  }

  /**
   * isNineHolesPlayed
   *
   * @param round
   * @returns
   */
  isNineHolesPlayed(round: Round) {
    if (round.round_mode === ROUND.ROUND_MODE_SIMPLE) {
      return round.number_of_holes_played === 9;
    }
    return round.holes.length === 9;
  }

  /**
   * isRoundScoreValid
   *
   * @param score
   * @param isNineHolesPlayed
   * @returns
   */
  isRoundScoreValid(score: number, isNineHolesPlayed: boolean) {
    if (isNineHolesPlayed) {
      return score >= 18 && score <= 78;
    }
    return score >= 36 && score <= 144;
  }

  /**
   * simpleScoreToPar
   *
   * @param roundId
   * @returns
   */
  async simpleScoreToPar(roundId) {
    const query = this.roundRepo.createQueryBuilder();
    query.where({ id: roundId });
    query.select([
      'user_id',
      'id',
      'igolf_course_id',
      'front_9_score',
      'back_9_score',
      'eighteen_holes_score',
      'number_of_holes_played',
      'round_mode',
    ]);
    const round = await query.getRawOne();
    if (!round) {
      return;
    }
    if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
      const user = await this.userRepo.findOne({
        where: { id: round.user_id },
        select: ['email', 'gender'],
      });
      if (!user) {
        return;
      }
      let gender = user.gender;
      if (gender && gender.length > 10) {
        gender = 'male';
      }

      gender = gender ? gender?.toLowerCase() : 'male';
      const scorecard = await this.threePartyCourseService.getCourseScoreCardDetail(round.igolf_course_id);
      let scorecard_info = null;
      try {
        scorecard_info = gender == 'male' ? scorecard['menScorecardList'][0] : scorecard['wmnScorecardList'][0];
      } catch (error) {
        return null;
      }

      let is_par_out = false;
      let is_nine_hole = false;
      let round_score = 0;
      if (round.number_of_holes_played == 9) {
        is_nine_hole = true;
        round_score = round.front_9_score || round.back_9_score;
        if (round.front_9_score) {
          is_par_out = true;
        }
      } else {
        round_score = round.eighteen_holes_score;
      }

      // # play 9 hole
      let par_hole = 0;
      if (is_nine_hole) {
        par_hole = is_par_out ? scorecard_info['parOut'] : scorecard_info['parIn'];
      }

      // # play 18 hole
      else par_hole = parseInt(scorecard_info['parOut']) + parseInt(scorecard_info['parIn']);

      const score_to_par = +round_score - +par_hole;

      this.logger.log(`PAR HOLE: ${par_hole}, TOTAL SCORE: ${round_score}, SCORE_TO_PAR: ${score_to_par}`);
      // # update simple score to par
      await this.roundRepo.update({ id: round.id }, { simple_score_to_par: score_to_par });
    }
  }

  /**
   * calculatePlayerHandicapIndex
   *
   * @param userId
   * @returns
   */
  async calculatePlayerHandicapIndex(userId: number) {
    this.logger.log(`CALCULATE PLAYER HANDICAP INDEX USER: ${userId}`);
    const roundScores = {};
    const user = await this.usersService.findOne({ id: userId });
    if (!user) {
      return null;
    }

    const rounds = await this.getRoundsForCalculatingHandicap(userId, 40);
    let validRounds = [];
    for (const round of rounds) {
      if (validRounds.length === 20) {
        continue;
      }
      const holesOfRound = await this.holesPlayedService.findHolesInRounds([round.id]);
      const lstHoleScore = await this.holesPlayedService.getScoreListHole(round.round_mode, holesOfRound, round.id);
      round['holes'] = lstHoleScore;
      const roundScore = this.getScore(round);
      roundScores[round.id] = roundScore;
      const isNineHolesPlayed = this.isNineHolesPlayed(round);
      const roundScoreValid = this.isRoundScoreValid(roundScore, isNineHolesPlayed);
      if (roundScoreValid) {
        validRounds.push(round);
      }
    }
    if (validRounds.length < 3) {
      this.logger.log(`VALID ROUNDS < 3: ${validRounds.length}`);
      return null;
    }
    validRounds = validRounds.reverse();

    let scoreDiffs = [];
    for (const round of validRounds) {
      if (!round.igolf_course_id) {
        this.logger.debug(`IGOLF_COURSE_ID EMPTY CONTINUE...`);
        continue;
      }
      try {
        let slope;
        let rating;
        const iGolfTee = await this.threePartyCourseService.getCourseTeeDetail(round.igolf_course_id);
        const tee_info =
          iGolfTee?.teesList?.find((item) => item.teeName?.toLowerCase() === round.tee_name.toLowerCase()) || null;
        if (!tee_info) {
          this.logger.debug(`TEE_INFO EMPTY CONTINUE...`);
          continue;
        }
        let gender = user.gender?.toLowerCase() || UserGender.MALE;
        if (gender && gender.length > 10) {
          gender = UserGender.MALE;
        }
        if (gender === 'men') {
          gender = UserGender.MALE;
        }
        if (gender === UserGender.MALE) {
          slope = tee_info.slopeMen;
          rating = tee_info.ratingMen;
        } else {
          slope = tee_info.slopeWomen;
          rating = tee_info.ratingWomen;
        }
        const roundScore = roundScores[round.id];
        const isNineHolesPlayed = this.isNineHolesPlayed(round);
        if (isNineHolesPlayed) {
          rating = parseFloat(rating) / 2;
        }
        if (parseFloat(slope) > 0) {
          const calculateScoreDiff = roundNumber(
            (HANDICAP_CONSTANT / parseFloat(slope)) * (roundScore - parseFloat(rating)),
            2
          );
          scoreDiffs.push(calculateScoreDiff);
        }
      } catch (error) {
        this.logger.error(error);
        continue;
      }
    }
    scoreDiffs = _.sortBy(scoreDiffs.slice(0, 20));
    if (scoreDiffs.length < 3) {
      this.logger.debug(`SCORE DIFFS LENGTH INVALID`);
      this.logger.debug({ scoreDiffs });
      return null;
    }
    const handicapConfigs = CdmService.getHandicapConfigsByTotalRounds(scoreDiffs.length);
    this.logger.log({ handicapConfigs });
    let totalHandicap = 0;
    for (let i = 0; i < handicapConfigs.scoresToCount; i++) {
      totalHandicap += scoreDiffs[i] + handicapConfigs.handicapAdjustment;
    }
    const handicap = CdmService.getHandicap(roundNumber(totalHandicap / handicapConfigs.scoresToCount, 1));
    this.logger.debug({ handicap });
    return handicap;
  }

  /**
   * validateRoundScore
   *
   * @param holeNumber
   * @param total_score
   */
  validateRoundScore(holeNumber: any, total_score: number) {
    let isValid = true;
    total_score = +total_score;
    if (holeNumber <= 9) {
      isValid = total_score >= 27 && total_score <= 90;
      if (!isValid) {
        throw new BadRequestException('Total score should be between 27 and 90');
      }
    } else if (holeNumber >= 10 && holeNumber < 18) {
      isValid = total_score >= 30 && total_score <= 180;
      if (!isValid) {
        throw new BadRequestException('Total score should be between 30 and 180');
      }
    } else {
      isValid = total_score >= 54 && total_score <= 180;
      if (!isValid) {
        throw new BadRequestException('Total score should be between 30 and 180');
      }
    }
  }

  /**
   * deleteRound
   *
   * @param id
   * @returns
   */
  async deleteRound(id: number) {
    const round = await this.roundRepo.findOne({ where: { id: id }, select: ['user_id', 'id', 'ghin_round_id'] });
    if (!round) {
      this.logger.log(`Not found round`);
      return { success: false };
    }

    await this.roundRepo.update({ id }, { deleted_at: new Date().toISOString() });
  }

  async roundsPlayed(userId) {
    if (isValidId(userId)) {
      const sql = `SELECT round_mode, count(round_mode) as totals FROM rounds WHERE user_id = ${+userId} AND deleted_at IS NULL GROUP BY round_mode`;
      const roundsPlayed = await this.roundRepo.query(sql);
      const user = await this.userRepo.findOne({
        where: { id: userId },
        withDeleted: true,
      });
      return { user, roundsPlayed };
    }
    throw new BadRequestException('User not found');
  }

  async validateRoundUSGA(roundData) {
    let round = null;
    if (roundData && roundData.id) {
      round = await this.roundRepo.findOne({
        where: { id: roundData.id },
      });

      if (isEmpty(round)) {
        throw new NotFoundException('Round not found');
      }

      if (round.ghin_round_id) {
        throw new BadRequestException('The round has posted score to GHIN!');
      }
    }

    const user = await this.userRepo.findOne({
      where: { id: roundData.user_id },
      select: ['gender', 'id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (!user.ghin_id) {
      throw new UnauthorizedException(`User don't have a GHIN id`);
    }
    if (!user.ghin_gpa_status) {
      throw new UnauthorizedException(`User need to acknowledge request golfer product access!`);
    }
    return round;
  }
}
